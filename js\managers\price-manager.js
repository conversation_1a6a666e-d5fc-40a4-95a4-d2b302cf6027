/**
 * 价格管理器模块
 * 负责价格转换、货币处理和价格显示逻辑
 * 支持多种货币转换和实时价格更新
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 价格管理器类
     * 负责价格相关的所有操作
     */
    class PriceManager {
        constructor(elements) {
            this.elements = elements;
            this.currencyConverter = null;
        }

        /**
         * 获取国际化管理器
         */
        getI18nManager() {
            return window.OTA?.i18nManager || window.i18nManager;
        }

        /**
         * 初始化价格管理器
         */
        init() {
            this.currencyConverter = window.CurrencyConverter ? new window.CurrencyConverter() : null;
            this.createPriceConversionDisplay();
            this.setupPriceValidation();
            getLogger().log('价格管理器初始化完成', 'success');
        }

        /**
         * 创建价格转换显示元素
         */
        createPriceConversionDisplay() {
            const priceGroup = document.querySelector('.price-input-group');
            if (!priceGroup || priceGroup.querySelector('.price-conversion-display')) {
                return; // 已存在或找不到容器
            }

            const conversionDisplay = document.createElement('div');
            conversionDisplay.className = 'price-conversion-display';
            conversionDisplay.innerHTML = `
                <div class="conversion-info">
                    <div class="original-price"></div>
                    <div class="conversion-arrow">→</div>
                    <div class="converted-price"></div>
                </div>
                <div class="conversion-rate"></div>
            `;
            conversionDisplay.style.display = 'none';
            priceGroup.appendChild(conversionDisplay);

            getLogger().log('价格转换显示元素已创建', 'info');
        }

        /**
         * 更新实时价格转换显示
         */
        updatePriceConversion() {
            const priceInput = this.elements.otaPrice;
            const currencySelect = this.elements.currency;
            const conversionDisplay = document.querySelector('.price-conversion-display');
            const originalPriceDisplay = conversionDisplay?.querySelector('.original-price');
            const convertedPriceDisplay = conversionDisplay?.querySelector('.converted-price');
            const conversionRate = conversionDisplay?.querySelector('.conversion-rate');

            if (!priceInput || !currencySelect || !conversionDisplay || !conversionRate) {
                return;
            }

            const amount = parseFloat(priceInput.value);
            const fromCurrency = currencySelect.value;

            // 如果没有输入价格或货币，隐藏转换显示
            if (!amount || amount <= 0 || !fromCurrency) {
                conversionDisplay.style.display = 'none';
                return;
            }

            // 使用货币转换器进行转换
            if (this.currencyConverter) {
                const conversionResult = this.currencyConverter.convertToMYR(amount, fromCurrency);
                
                if (conversionResult.needsConversion) {
                    // 显示转换信息：原价 → 转换后
                    const originalPrice = this.currencyConverter.formatPrice(amount, fromCurrency);
                    const convertedPrice = this.currencyConverter.formatPrice(conversionResult.convertedAmount, 'MYR');
                    const i18n = this.getI18nManager();
                    
                    if (originalPriceDisplay) {
                        const originalText = i18n ? i18n.t('price.originalPrice') : '原价';
                        originalPriceDisplay.textContent = `${originalText} ${originalPrice}`;
                    }
                    if (convertedPriceDisplay) {
                        const convertedText = i18n ? i18n.t('price.convertedPrice') : '转换后价格';
                        convertedPriceDisplay.textContent = `${convertedText} ${convertedPrice}`;
                    }
                    if (conversionRate) {
                        const rateText = i18n ? i18n.t('price.exchangeRate') : '汇率';
                        conversionRate.textContent = `${rateText}: 1 ${fromCurrency} = ${conversionResult.exchangeRate} MYR`;
                    }
                    
                    conversionDisplay.style.display = 'block';
                    getLogger().log('价格转换显示已更新', 'info', {
                        original: originalPrice,
                        converted: convertedPrice,
                        rate: conversionResult.exchangeRate
                    });
                } else {
                    // MYR订单，隐藏转换显示
                    conversionDisplay.style.display = 'none';
                    getLogger().log('MYR订单，隐藏价格转换显示', 'info');
                }
            } else {
                getLogger().log('货币转换器未初始化', 'warning');
                conversionDisplay.style.display = 'none';
            }
        }

        /**
         * 验证价格输入
         */
        validatePriceInput() {
            const priceInput = this.elements.otaPrice;
            const priceGroup = document.querySelector('.price-input-group');

            if (!priceInput || !priceGroup) {
                return;
            }

            const amount = parseFloat(priceInput.value);

            // 移除之前的验证状态
            priceGroup.classList.remove('invalid', 'valid');

            if (priceInput.value && (isNaN(amount) || amount <= 0)) {
                priceGroup.classList.add('invalid');
            } else if (amount > 0) {
                priceGroup.classList.add('valid');
            }
        }

        /**
         * 设置价格验证
         */
        setupPriceValidation() {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.addEventListener('input', () => {
                    this.validatePriceInput();
                    this.updatePriceConversion();
                });

                this.elements.otaPrice.addEventListener('blur', () => {
                    this.validatePriceInput();
                });
            }

            if (this.elements.currency) {
                this.elements.currency.addEventListener('change', () => {
                    this.updatePriceConversion();
                });
            }
        }

        /**
         * 处理Gemini分析结果中的价格转换
         * @param {Object} orderData - 订单数据
         */
        processPriceConversion(orderData) {
            if (!orderData) {
                return;
            }

            try {
                if (!this.currencyConverter) {
                    getLogger().log('货币转换器未初始化', 'warning');
                    return;
                }

                // 处理订单价格转换
                if (orderData.ota_price && orderData.currency) {
                    // 处理订单价格转换
                    const processedData = this.currencyConverter.processOrderPrice({
                        price: orderData.ota_price,
                        currency: orderData.currency
                    });

                    // 更新表单字段
                    if (this.elements.otaPrice) {
                        this.elements.otaPrice.value = processedData.originalPrice || orderData.ota_price;
                    }
                    if (this.elements.currency) {
                        this.elements.currency.value = processedData.originalCurrency || orderData.currency;
                    }

                    // 更新价格转换显示
                    this.updatePriceConversion();

                    getLogger().log('价格转换处理完成', 'success', {
                        originalPrice: processedData.originalPrice,
                        originalCurrency: processedData.originalCurrency,
                        convertedAmount: processedData.convertedAmount
                    });
                }
            } catch (error) {
                getLogger().log('价格转换处理失败', 'error', { error: error.message });
            }
        }

        /**
         * 获取价格转换结果
         * @param {number} amount - 金额
         * @param {string} fromCurrency - 源货币
         * @returns {Object} 转换结果
         */
        getConversionResult(amount, fromCurrency) {
            if (!this.currencyConverter) {
                return null;
            }

            return this.currencyConverter.convertToMYR(amount, fromCurrency);
        }

        /**
         * 格式化价格显示
         * @param {number} amount - 金额
         * @param {string} currency - 货币
         * @returns {string} 格式化后的价格字符串
         */
        formatPrice(amount, currency) {
            if (!this.currencyConverter) {
                return `${amount} ${currency}`;
            }

            return this.currencyConverter.formatPrice(amount, currency);
        }

        /**
         * 检查是否需要价格转换
         * @param {string} currency - 货币类型
         * @returns {boolean} 是否需要转换
         */
        needsConversion(currency) {
            if (!this.currencyConverter) {
                return false;
            }

            const result = this.currencyConverter.convertToMYR(100, currency);
            return result.needsConversion;
        }

        /**
         * 获取支持的货币列表
         * @returns {Array} 支持的货币列表
         */
        getSupportedCurrencies() {
            if (!this.currencyConverter) {
                return ['MYR', 'USD', 'SGD', 'CNY'];
            }

            return this.currencyConverter.getSupportedCurrencies();
        }

        /**
         * 获取汇率信息
         * @param {string} fromCurrency - 源货币
         * @param {string} toCurrency - 目标货币
         * @returns {number} 汇率
         */
        getExchangeRate(fromCurrency, toCurrency = 'MYR') {
            if (!this.currencyConverter) {
                return 1;
            }

            const result = this.currencyConverter.convertToMYR(1, fromCurrency);
            return result.exchangeRate;
        }

        /**
         * 重置价格相关字段
         */
        resetPriceFields() {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.value = '';
            }
            if (this.elements.currency) {
                this.elements.currency.value = 'MYR';
            }

            // 隐藏价格转换显示
            const conversionDisplay = document.querySelector('.price-conversion-display');
            if (conversionDisplay) {
                conversionDisplay.style.display = 'none';
            }

            // 移除验证状态
            const priceGroup = document.querySelector('.price-input-group');
            if (priceGroup) {
                priceGroup.classList.remove('invalid', 'valid');
            }

            getLogger().log('价格字段已重置', 'info');
        }

        /**
         * 设置价格和货币
         * @param {number} amount - 金额
         * @param {string} currency - 货币
         */
        setPriceAndCurrency(amount, currency) {
            if (this.elements.otaPrice) {
                this.elements.otaPrice.value = amount;
            }
            if (this.elements.currency) {
                this.elements.currency.value = currency;
            }

            // 触发验证和转换更新
            this.validatePriceInput();
            this.updatePriceConversion();

            getLogger().log('价格和货币已设置', 'info', { amount, currency });
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.PriceManager = PriceManager;

})();
