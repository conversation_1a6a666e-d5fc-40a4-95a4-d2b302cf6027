/**
 * 统一数据管理器
 * 建立单一数据源，管理系统中的所有静态和动态数据
 * 提供智能缓存和数据同步机制
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * 统一数据管理器类
     */
    class UnifiedDataManager {
        constructor() {
            this.dataSources = new Map(); // 数据源注册表
            this.cache = new Map(); // 主缓存
            this.subscribers = new Map(); // 订阅者
            this.initialized = false;
            this.logger = null;
            
            // 数据更新策略
            this.updateStrategies = {
                immediate: this.immediateUpdate.bind(this),
                debounced: this.debouncedUpdate.bind(this),
                batched: this.batchedUpdate.bind(this)
            };
            
            // 缓存策略
            this.cacheStrategies = {
                lru: this.lruEviction.bind(this),
                ttl: this.ttlEviction.bind(this),
                manual: this.manualEviction.bind(this)
            };
        }

        /**
         * 初始化数据管理器
         */
        init() {
            if (this.initialized) {
                this.log('统一数据管理器已经初始化', 'warning');
                return;
            }

            // 注册核心数据源
            this.registerCoreDataSources();
            
            // 设置数据同步机制
            this.setupDataSync();
            
            this.initialized = true;
            this.log('统一数据管理器已初始化', 'info');
        }

        /**
         * 注册核心数据源
         */
        registerCoreDataSources() {
            // 语言数据源
            this.registerDataSource('languages', {
                primary: () => this.getFromApiService('languages'),
                secondary: () => this.getFromAppState('systemData.languages'),
                fallback: () => this.getFromLanguageManager(),
                cacheKey: 'core_languages',
                ttl: 300000, // 5分钟
                strategy: 'immediate'
            });

            // 后端用户数据源
            this.registerDataSource('backendUsers', {
                primary: () => this.getFromApiService('backend_users'),
                secondary: () => this.getFromAppState('systemData.backendUsers'),
                fallback: () => [],
                cacheKey: 'core_backend_users',
                ttl: 600000, // 10分钟
                strategy: 'debounced'
            });

            // 汽车类型数据源
            this.registerDataSource('carTypes', {
                primary: () => this.getFromApiService('car_types'),
                secondary: () => this.getFromAppState('systemData.carTypes'),
                fallback: () => [],
                cacheKey: 'core_car_types',
                ttl: 1800000, // 30分钟
                strategy: 'batched'
            });

            this.log('核心数据源已注册', 'info');
        }

        /**
         * 注册数据源
         * @param {string} dataType - 数据类型
         * @param {Object} config - 配置对象
         */
        registerDataSource(dataType, config) {
            const dataSource = {
                ...config,
                lastFetch: 0,
                fetchCount: 0,
                errorCount: 0,
                lastError: null
            };

            this.dataSources.set(dataType, dataSource);
            this.log(`已注册数据源: ${dataType}`, 'debug');
        }

        /**
         * 获取数据
         * @param {string} dataType - 数据类型
         * @param {Object} options - 选项
         * @returns {Promise<Array>} 数据数组
         */
        async getData(dataType, options = {}) {
            const {
                useCache = true,
                fallbackToCache = true,
                forceRefresh = false
            } = options;

            const dataSource = this.dataSources.get(dataType);
            if (!dataSource) {
                throw new Error(`未知的数据类型: ${dataType}`);
            }

            const cacheKey = dataSource.cacheKey;

            // 检查缓存
            if (useCache && !forceRefresh) {
                const cachedData = this.getFromCache(cacheKey);
                if (cachedData && this.isCacheValid(cacheKey, dataSource.ttl)) {
                    this.log(`从缓存获取 ${dataType} 数据`, 'debug');
                    return cachedData;
                }
            }

            try {
                // 尝试从主数据源获取
                let data = await this.fetchFromDataSource(dataSource.primary, 'primary');
                
                if (!data || data.length === 0) {
                    // 尝试从次数据源获取
                    data = await this.fetchFromDataSource(dataSource.secondary, 'secondary');
                }
                
                if (!data || data.length === 0) {
                    // 使用降级数据源
                    data = await this.fetchFromDataSource(dataSource.fallback, 'fallback');
                }

                if (data && data.length > 0) {
                    // 更新缓存
                    this.updateCache(cacheKey, data);
                    
                    // 更新统计信息
                    dataSource.lastFetch = Date.now();
                    dataSource.fetchCount++;
                    
                    // 通知订阅者
                    this.notifySubscribers(dataType, data);
                    
                    this.log(`成功获取 ${dataType} 数据: ${data.length} 项`, 'info');
                    return data;
                }
                
                throw new Error(`所有数据源都返回空数据`);

            } catch (error) {
                dataSource.errorCount++;
                dataSource.lastError = error.message;
                
                this.log(`获取 ${dataType} 数据失败: ${error.message}`, 'error');

                // 如果允许降级到缓存，返回过期的缓存数据
                if (fallbackToCache) {
                    const cachedData = this.getFromCache(cacheKey);
                    if (cachedData) {
                        this.log(`使用过期缓存数据 ${dataType}`, 'warning');
                        return cachedData;
                    }
                }

                throw error;
            }
        }

        /**
         * 从数据源获取数据
         * @param {Function} dataSourceFn - 数据源函数
         * @param {string} sourceType - 数据源类型
         * @returns {Promise<Array>} 数据
         */
        async fetchFromDataSource(dataSourceFn, sourceType) {
            try {
                if (typeof dataSourceFn !== 'function') {
                    return null;
                }

                const result = await dataSourceFn();
                this.log(`从 ${sourceType} 数据源获取数据成功`, 'debug');
                return result;
                
            } catch (error) {
                this.log(`从 ${sourceType} 数据源获取数据失败: ${error.message}`, 'debug');
                return null;
            }
        }

        /**
         * 从ApiService获取数据
         * @param {string} dataKey - 数据键
         * @returns {Array} 数据数组
         */
        getFromApiService(dataKey) {
            try {
                const apiService = getApiService ? getApiService() : null;
                return apiService?.staticData?.[dataKey] || [];
            } catch (error) {
                return [];
            }
        }

        /**
         * 从AppState获取数据
         * @param {string} path - 数据路径
         * @returns {Array} 数据数组
         */
        getFromAppState(path) {
            try {
                const appState = getAppState ? getAppState() : null;
                const systemData = appState?.get('systemData') || {};
                
                const keys = path.split('.');
                let data = systemData;
                
                for (const key of keys.slice(1)) { // 跳过 'systemData'
                    data = data?.[key];
                    if (data === undefined) break;
                }
                
                return Array.isArray(data) ? data : [];
            } catch (error) {
                return [];
            }
        }

        /**
         * 从语言管理器获取数据
         * @returns {Array} 语言数据数组
         */
        getFromLanguageManager() {
            try {
                const languageManager = getLanguageManager ? getLanguageManager() : null;
                return languageManager?.getLanguagesSync({ enabledOnly: true }) || [];
            } catch (error) {
                return [];
            }
        }

        /**
         * 订阅数据更新
         * @param {string} dataType - 数据类型
         * @param {Function} callback - 回调函数
         * @returns {string} 订阅ID
         */
        subscribe(dataType, callback) {
            if (!this.subscribers.has(dataType)) {
                this.subscribers.set(dataType, new Map());
            }

            const subscriptionId = `${dataType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            this.subscribers.get(dataType).set(subscriptionId, callback);

            this.log(`已订阅 ${dataType} 数据更新`, 'debug');
            return subscriptionId;
        }

        /**
         * 取消订阅
         * @param {string} dataType - 数据类型
         * @param {string} subscriptionId - 订阅ID
         */
        unsubscribe(dataType, subscriptionId) {
            const typeSubscribers = this.subscribers.get(dataType);
            if (typeSubscribers) {
                typeSubscribers.delete(subscriptionId);
                this.log(`已取消订阅 ${dataType} 数据更新`, 'debug');
            }
        }

        /**
         * 通知订阅者
         * @param {string} dataType - 数据类型
         * @param {Array} data - 数据
         */
        notifySubscribers(dataType, data) {
            const typeSubscribers = this.subscribers.get(dataType);
            if (typeSubscribers) {
                typeSubscribers.forEach((callback, subscriptionId) => {
                    try {
                        callback(data, dataType);
                    } catch (error) {
                        this.log(`订阅者回调执行失败 ${subscriptionId}: ${error.message}`, 'error');
                    }
                });
            }
        }

        /**
         * 更新缓存
         * @param {string} key - 缓存键
         * @param {any} data - 数据
         */
        updateCache(key, data) {
            this.cache.set(key, {
                data: Array.isArray(data) ? [...data] : data,
                timestamp: Date.now(),
                accessCount: 0,
                lastAccess: Date.now()
            });
        }

        /**
         * 从缓存获取数据
         * @param {string} key - 缓存键
         * @returns {any} 缓存数据
         */
        getFromCache(key) {
            const cached = this.cache.get(key);
            if (cached) {
                cached.accessCount++;
                cached.lastAccess = Date.now();
                return cached.data;
            }
            return null;
        }

        /**
         * 检查缓存是否有效
         * @param {string} key - 缓存键
         * @param {number} ttl - 生存时间（毫秒）
         * @returns {boolean} 是否有效
         */
        isCacheValid(key, ttl) {
            const cached = this.cache.get(key);
            if (!cached) return false;
            
            return (Date.now() - cached.timestamp) < ttl;
        }

        /**
         * 清理缓存
         * @param {string} key - 缓存键（可选）
         */
        clearCache(key = null) {
            if (key) {
                this.cache.delete(key);
                this.log(`已清理缓存: ${key}`, 'debug');
            } else {
                this.cache.clear();
                this.log('已清理所有缓存', 'info');
            }
        }

        /**
         * 设置数据同步机制
         */
        setupDataSync() {
            // 监听AppState变化
            try {
                const appState = getAppState ? getAppState() : null;
                if (appState && typeof appState.subscribe === 'function') {
                    appState.subscribe('systemData', (newData) => {
                        this.handleAppStateUpdate(newData);
                    });
                }
            } catch (error) {
                this.log(`设置AppState同步失败: ${error.message}`, 'warning');
            }
        }

        /**
         * 处理AppState更新
         * @param {Object} newData - 新数据
         */
        handleAppStateUpdate(newData) {
            this.log('检测到AppState数据更新', 'debug');

            // 更新相关缓存
            if (newData.languages) {
                this.updateCache('core_languages', newData.languages);
                this.notifySubscribers('languages', newData.languages);
            }

            if (newData.backendUsers) {
                this.updateCache('core_backend_users', newData.backendUsers);
                this.notifySubscribers('backendUsers', newData.backendUsers);
            }

            if (newData.carTypes) {
                this.updateCache('core_car_types', newData.carTypes);
                this.notifySubscribers('carTypes', newData.carTypes);
            }
        }

        /**
         * 获取统计信息
         * @returns {Object} 统计信息
         */
        getStats() {
            const stats = {
                dataSources: this.dataSources.size,
                cacheSize: this.cache.size,
                subscribers: 0,
                cacheHitRatio: 0,
                sources: {}
            };

            // 计算订阅者总数
            this.subscribers.forEach(typeSubscribers => {
                stats.subscribers += typeSubscribers.size;
            });

            // 计算数据源统计
            this.dataSources.forEach((source, type) => {
                stats.sources[type] = {
                    fetchCount: source.fetchCount,
                    errorCount: source.errorCount,
                    lastFetch: source.lastFetch,
                    lastError: source.lastError
                };
            });

            // 计算缓存命中率
            let totalAccess = 0;
            let totalHits = 0;

            this.cache.forEach(cached => {
                totalAccess += cached.accessCount;
                if (cached.accessCount > 0) {
                    totalHits++;
                }
            });

            stats.cacheHitRatio = totalAccess > 0 ? (totalHits / totalAccess * 100).toFixed(2) : 0;

            return stats;
        }

        /**
         * 立即更新策略
         */
        immediateUpdate(dataType, data) {
            this.notifySubscribers(dataType, data);
        }

        /**
         * 防抖更新策略
         */
        debouncedUpdate(dataType, data) {
            clearTimeout(this.debounceTimers?.[dataType]);
            
            if (!this.debounceTimers) {
                this.debounceTimers = {};
            }
            
            this.debounceTimers[dataType] = setTimeout(() => {
                this.notifySubscribers(dataType, data);
            }, 300);
        }

        /**
         * 批量更新策略
         */
        batchedUpdate(dataType, data) {
            if (!this.batchQueue) {
                this.batchQueue = new Map();
            }
            
            this.batchQueue.set(dataType, data);
            
            if (!this.batchTimer) {
                this.batchTimer = setTimeout(() => {
                    this.batchQueue.forEach((data, type) => {
                        this.notifySubscribers(type, data);
                    });
                    
                    this.batchQueue.clear();
                    this.batchTimer = null;
                }, 100);
            }
        }

        /**
         * LRU缓存淘汰
         */
        lruEviction(maxSize = 50) {
            if (this.cache.size <= maxSize) return;

            const entries = Array.from(this.cache.entries())
                .sort((a, b) => a[1].lastAccess - b[1].lastAccess);

            const toRemove = entries.slice(0, this.cache.size - maxSize);
            toRemove.forEach(([key]) => {
                this.cache.delete(key);
            });

            this.log(`LRU清理了 ${toRemove.length} 个缓存项`, 'debug');
        }

        /**
         * TTL缓存淘汰
         */
        ttlEviction() {
            const now = Date.now();
            const expired = [];

            this.cache.forEach((cached, key) => {
                const dataSource = Array.from(this.dataSources.values())
                    .find(ds => ds.cacheKey === key);
                
                const ttl = dataSource?.ttl || 300000; // 默认5分钟
                
                if (now - cached.timestamp > ttl) {
                    expired.push(key);
                }
            });

            expired.forEach(key => {
                this.cache.delete(key);
            });

            if (expired.length > 0) {
                this.log(`TTL清理了 ${expired.length} 个过期缓存项`, 'debug');
            }
        }

        /**
         * 手动缓存淘汰
         */
        manualEviction() {
            // 由外部调用clearCache进行手动清理
        }

        /**
         * 日志输出
         * @param {string} message - 消息
         * @param {string} level - 级别
         * @param {Object} data - 数据
         */
        log(message, level = 'info', data = null) {
            if (!this.logger) {
                try {
                    this.logger = getLogger ? getLogger() : null;
                } catch (e) {
                    // Logger可能还未初始化
                }
            }

            if (this.logger) {
                this.logger.log(`[DataManager] ${message}`, level, data);
            } else {
                console.log(`[DataManager] ${message}`, data);
            }
        }
    }

    // 创建全局实例
    const unifiedDataManager = new UnifiedDataManager();

    // 导出到OTA命名空间
    window.OTA.UnifiedDataManager = UnifiedDataManager;
    window.OTA.unifiedDataManager = unifiedDataManager;

    // 向后兼容
    window.UnifiedDataManager = UnifiedDataManager;
    window.unifiedDataManager = unifiedDataManager;

    // 🔧 注册到依赖容器（如果可用）
    if (window.OTA && window.OTA.container && typeof window.OTA.container.register === 'function') {
        try {
            window.OTA.container.register('dataManager', () => unifiedDataManager, {
                singleton: true
            });
        } catch (error) {
            console.warn('[UnifiedDataManager] 注册到依赖容器失败:', error.message);
        }
    }

})();