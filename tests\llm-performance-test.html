<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM性能对比测试 - <PERSON> vs <PERSON><PERSON></title>
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/components/buttons.css">
    <link rel="stylesheet" href="../css/components/cards.css">
    <link rel="stylesheet" href="../css/components/forms.css">
    <link rel="stylesheet" href="../css/layout/header.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .test-controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-input-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .test-textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .test-textarea:focus {
            border-color: #667eea;
            outline: none;
        }

        .quick-examples {
            margin-top: 15px;
        }

        .example-btn {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 8px 12px;
            margin: 5px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .example-btn:hover {
            background: #e9ecef;
            border-color: #adb5bd;
        }

        .test-actions {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-btn {
            padding: 12px 30px;
            margin: 0 10px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .test-btn.single-order {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .test-btn.multi-order {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.2);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .results-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }

        .result-panel {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .result-header {
            padding: 15px 20px;
            color: white;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .result-header.gemini {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .result-header.kimi {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .result-content {
            padding: 20px;
            max-height: 600px;
            overflow-y: auto;
        }

        .performance-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }

        .metric-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }

        .result-json {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .error-message {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .comparison-summary {
            margin-top: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .comparison-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .winner {
            color: #28a745;
            font-weight: bold;
        }

        .loser {
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .test-controls {
                grid-template-columns: 1fr;
            }

            .results-container {
                grid-template-columns: 1fr;
            }

            .performance-metrics {
                grid-template-columns: repeat(2, 1fr);
            }

            .test-btn {
                display: block;
                width: 100%;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- 测试页面头部 -->
        <div class="test-header">
            <h1>🚀 LLM性能对比测试</h1>
            <p>Gemini 2.5 Flash vs Kimi K2 - 订单解析能力与性能对比</p>
        </div>

        <!-- 输入控制区域 -->
        <div class="test-controls">
            <div class="test-input-section">
                <h3>📝 测试订单内容</h3>
                <textarea 
                    id="orderText" 
                    class="test-textarea" 
                    placeholder="请输入要测试的订单文本内容...&#10;&#10;支持单订单或多订单文本，系统会自动识别并解析"></textarea>
                
                <div class="quick-examples">
                    <h4>快速示例：</h4>
                    <button class="example-btn" onclick="loadExample('single')">单订单示例</button>
                    <button class="example-btn" onclick="loadExample('multi')">多订单示例</button>
                    <button class="example-btn" onclick="loadExample('complex')">复杂订单示例</button>
                    <button class="example-btn" onclick="clearInput()">清空内容</button>
                </div>
            </div>

            <div class="test-input-section">
                <h3>⚙️ 测试配置</h3>
                <div style="margin-bottom: 15px;">
                    <label>
                        <input type="checkbox" id="includeMetrics" checked> 
                        包含性能指标分析
                    </label>
                </div>
                <div style="margin-bottom: 15px;">
                    <label>
                        <input type="checkbox" id="showRawResponse" checked> 
                        显示原始响应内容
                    </label>
                </div>
                <div style="margin-bottom: 15px;">
                    <label>
                        <input type="checkbox" id="autoCompare" checked> 
                        自动进行结果对比
                    </label>
                </div>
                <div>
                    <label for="testRounds">测试轮数：</label>
                    <select id="testRounds">
                        <option value="1" selected>1轮</option>
                        <option value="3">3轮</option>
                        <option value="5">5轮</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 测试执行按钮 -->
        <div class="test-actions">
            <button id="testSingleBtn" class="test-btn single-order" onclick="runSingleTest()">
                🔍 单订单模式测试
            </button>
            <button id="testMultiBtn" class="test-btn multi-order" onclick="runMultiTest()">
                📋 多订单模式测试
            </button>
            <button id="testBothBtn" class="test-btn" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%); color: white;" onclick="runBothTests()">
                ⚡ 同时测试对比
            </button>
        </div>

        <!-- 结果显示区域 -->
        <div class="results-container" id="resultsContainer" style="display: none;">
            <!-- Gemini 结果 -->
            <div class="result-panel">
                <div class="result-header gemini">
                    <span>🤖 Gemini 2.5 Flash</span>
                    <span id="geminiStatus"></span>
                </div>
                <div class="result-content">
                    <div id="geminiMetrics" class="performance-metrics"></div>
                    <div id="geminiResult"></div>
                </div>
            </div>

            <!-- Kimi 结果 -->
            <div class="result-panel">
                <div class="result-header kimi">
                    <span>🌙 Kimi K2</span>
                    <span id="kimiStatus"></span>
                </div>
                <div class="result-content">
                    <div id="kimiMetrics" class="performance-metrics"></div>
                    <div id="kimiResult"></div>
                </div>
            </div>
        </div>

        <!-- 对比总结 -->
        <div class="comparison-summary" id="comparisonSummary" style="display: none;">
            <h3>📊 性能对比总结</h3>
            <div id="comparisonContent"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/core/dependency-container.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/gemini-service.js"></script>
    <script src="../js/kimi-service.js"></script>

    <script>
        // 全局测试状态
        let testState = {
            isRunning: false,
            currentTest: null,
            results: {
                gemini: null,
                kimi: null
            },
            startTime: null
        };

        // 示例数据
        const examples = {
            single: `接机服务
客户：张三
电话：+60123456789
航班：MH123
日期：2024-12-25 14:30
地点：KLIA2 到 吉隆坡希尔顿酒店
乘客：2人
行李：1件
参考号：CD123456`,

            multi: `订单1：
接机 - 李四 +60123456780 MH456 2024-12-26 16:00 KLIA2→槟城酒店 3人 2行李 REF001

订单2：
送机 - 王五 +60123456781 AK789 2024-12-27 09:30 酒店→机场 1人 1行李 REF002

订单3：
包车 - 赵六 +60123456782 2024-12-28 08:00 酒店→云顶高原 4人 3行李 REF003`,

            complex: `紧急订单处理：
1. 客户：陈七 (华人客户)
   联系方式：+60-12-345-6789 / <EMAIL>
   服务：机场接机服务
   航班信息：马来西亚航空 MH370 预计14:30抵达
   接机地点：吉隆坡国际机场KLIA Terminal 1
   目的地：吉隆坡市中心双子塔附近酒店
   乘客人数：3人 (2大1小，需要儿童座椅)
   行李：2个大箱子 + 1个随身包
   特殊需求：需要中文司机，举牌接机
   订单号：CHONG-789456123
   价格：MYR 180
   
2. 客户：Smith John
   电话：+60-11-567-8901
   服务类型：送机
   出发时间：2024-12-29 早上06:00
   从：Sunway Pyramid Hotel Kuala Lumpur  
   到：Kuala Lumpur International Airport KLIA2
   航班：亚航AK6248 09:15起飞
   人数：2人
   行李：3件
   车型要求：5座舒适型
   参考编号：KL-AK-567891`
        };

        // 加载示例数据
        function loadExample(type) {
            const textarea = document.getElementById('orderText');
            textarea.value = examples[type] || '';
            textarea.focus();
        }

        // 清空输入
        function clearInput() {
            document.getElementById('orderText').value = '';
            hideResults();
        }

        // 运行单订单测试
        async function runSingleTest() {
            await runTest('single');
        }

        // 运行多订单测试  
        async function runMultiTest() {
            await runTest('multi');
        }

        // 同时运行两个模型的测试
        async function runBothTests() {
            await runTest('both');
        }

        // 主要测试函数
        async function runTest(testType) {
            const orderText = document.getElementById('orderText').value.trim();
            
            if (!orderText) {
                alert('请输入订单文本内容');
                return;
            }

            if (testState.isRunning) {
                alert('测试正在进行中，请等待完成');
                return;
            }

            try {
                testState.isRunning = true;
                testState.currentTest = testType;
                testState.startTime = Date.now();
                
                // 更新UI状态
                updateTestButtons(true);
                showResults();
                clearResults();

                // 获取测试配置
                const config = getTestConfig();
                
                // 根据测试类型执行不同的测试策略
                if (testType === 'both') {
                    await runParallelTest(orderText, config);
                } else {
                    await runSequentialTest(orderText, config, testType);
                }

                // 如果开启自动对比，生成对比结果
                if (config.autoCompare && testState.results.gemini && testState.results.kimi) {
                    generateComparison();
                }

            } catch (error) {
                console.error('测试执行失败:', error);
                showError('测试执行失败: ' + error.message);
            } finally {
                testState.isRunning = false;
                updateTestButtons(false);
            }
        }

        // 并行测试两个模型
        async function runParallelTest(orderText, config) {
            updateStatus('gemini', '🔄 正在测试...');
            updateStatus('kimi', '🔄 正在测试...');

            try {
                // 并行调用两个API
                const [geminiResult, kimiResult] = await Promise.allSettled([
                    testGeminiModel(orderText, config),
                    testKimiModel(orderText, config)
                ]);

                // 处理Gemini结果
                if (geminiResult.status === 'fulfilled') {
                    testState.results.gemini = geminiResult.value;
                    displayResult('gemini', geminiResult.value);
                    updateStatus('gemini', '✅ 完成');
                } else {
                    updateStatus('gemini', '❌ 失败');
                    displayError('gemini', geminiResult.reason.message);
                }

                // 处理Kimi结果
                if (kimiResult.status === 'fulfilled') {
                    testState.results.kimi = kimiResult.value;
                    displayResult('kimi', kimiResult.value);
                    updateStatus('kimi', '✅ 完成');
                } else {
                    updateStatus('kimi', '❌ 失败');
                    displayError('kimi', kimiResult.reason.message);
                }

            } catch (error) {
                console.error('并行测试失败:', error);
                updateStatus('gemini', '❌ 错误');
                updateStatus('kimi', '❌ 错误');
            }
        }

        // 顺序测试
        async function runSequentialTest(orderText, config, testType) {
            if (testType === 'single' || testType === 'gemini') {
                updateStatus('gemini', '🔄 正在测试...');
                try {
                    const result = await testGeminiModel(orderText, config);
                    testState.results.gemini = result;
                    displayResult('gemini', result);
                    updateStatus('gemini', '✅ 完成');
                } catch (error) {
                    updateStatus('gemini', '❌ 失败');
                    displayError('gemini', error.message);
                }
            }

            if (testType === 'multi' || testType === 'kimi') {
                updateStatus('kimi', '🔄 正在测试...');
                try {
                    const result = await testKimiModel(orderText, config);
                    testState.results.kimi = result;
                    displayResult('kimi', result);
                    updateStatus('kimi', '✅ 完成');
                } catch (error) {
                    updateStatus('kimi', '❌ 失败');
                    displayError('kimi', error.message);
                }
            }
        }

        // 测试Gemini模型
        async function testGeminiModel(orderText, config) {
            const geminiService = window.getGeminiService();
            const startTime = Date.now();
            
            try {
                const result = await geminiService.parseOrder(orderText);
                const endTime = Date.now();
                
                // Gemini服务直接返回数组或null
                const orders = Array.isArray(result) ? result : (result ? [result] : []);
                const success = result !== null && orders.length > 0;
                
                return {
                    service: 'Gemini',
                    success: success,
                    orders: orders,
                    error: success ? null : '解析失败或返回空结果',
                    metadata: {
                        responseTime: endTime - startTime,
                        inputLength: orderText.length,
                        outputLength: JSON.stringify(orders).length,
                        orderCount: orders.length
                    },
                    rawResponse: config.showRawResponse ? JSON.stringify(result, null, 2) : null
                };
            } catch (error) {
                return {
                    service: 'Gemini',
                    success: false,
                    orders: [],
                    error: error.message,
                    metadata: {
                        responseTime: Date.now() - startTime,
                        inputLength: orderText.length,
                        outputLength: 0,
                        orderCount: 0
                    }
                };
            }
        }

        // 测试Kimi模型
        async function testKimiModel(orderText, config) {
            const kimiService = window.getKimiService();
            const startTime = Date.now();
            
            try {
                const result = await kimiService.parseOrderText(orderText);
                const endTime = Date.now();
                
                return {
                    service: 'Kimi',
                    success: result.success,
                    orders: result.orders || [],
                    error: result.error,
                    metadata: {
                        ...result.metadata,
                        responseTime: endTime - startTime,
                        inputLength: orderText.length,
                        outputLength: JSON.stringify(result.orders || []).length,
                        orderCount: (result.orders || []).length
                    },
                    rawResponse: config.showRawResponse ? result.rawResponse : null
                };
            } catch (error) {
                return {
                    service: 'Kimi',
                    success: false,
                    orders: [],
                    error: error.message,
                    metadata: {
                        responseTime: Date.now() - startTime,
                        inputLength: orderText.length,
                        outputLength: 0,
                        orderCount: 0
                    }
                };
            }
        }

        // 获取测试配置
        function getTestConfig() {
            return {
                includeMetrics: document.getElementById('includeMetrics').checked,
                showRawResponse: document.getElementById('showRawResponse').checked,
                autoCompare: document.getElementById('autoCompare').checked,
                testRounds: parseInt(document.getElementById('testRounds').value)
            };
        }

        // 更新按钮状态
        function updateTestButtons(disabled) {
            const buttons = ['testSingleBtn', 'testMultiBtn', 'testBothBtn'];
            buttons.forEach(id => {
                const btn = document.getElementById(id);
                btn.disabled = disabled;
                if (disabled) {
                    btn.innerHTML = btn.innerHTML.replace(/^[^<]*/, '⏳ 测试中...');
                } else {
                    // 恢复原始文本
                    if (id === 'testSingleBtn') btn.innerHTML = '🔍 单订单模式测试';
                    else if (id === 'testMultiBtn') btn.innerHTML = '📋 多订单模式测试';
                    else if (id === 'testBothBtn') btn.innerHTML = '⚡ 同时测试对比';
                }
            });
        }

        // 更新状态显示
        function updateStatus(service, status) {
            document.getElementById(`${service}Status`).innerHTML = status;
        }

        // 显示结果区域
        function showResults() {
            document.getElementById('resultsContainer').style.display = 'grid';
        }

        // 隐藏结果区域
        function hideResults() {
            document.getElementById('resultsContainer').style.display = 'none';
            document.getElementById('comparisonSummary').style.display = 'none';
        }

        // 清空结果
        function clearResults() {
            ['gemini', 'kimi'].forEach(service => {
                document.getElementById(`${service}Metrics`).innerHTML = '';
                document.getElementById(`${service}Result`).innerHTML = '';
                updateStatus(service, '⏳ 准备中...');
            });
            
            testState.results.gemini = null;
            testState.results.kimi = null;
            
            document.getElementById('comparisonSummary').style.display = 'none';
        }

        // 显示测试结果
        function displayResult(service, result) {
            // 显示性能指标
            if (result.metadata) {
                displayMetrics(service, result.metadata);
            }

            // 显示结果内容
            const resultContainer = document.getElementById(`${service}Result`);
            
            if (!result.success) {
                resultContainer.innerHTML = `<div class="error-message">错误: ${result.error}</div>`;
                return;
            }

            let html = '';
            
            // 订单解析结果
            if (result.orders && result.orders.length > 0) {
                html += `<h4>解析结果 (${result.orders.length}个订单):</h4>`;
                html += `<div class="result-json">${JSON.stringify(result.orders, null, 2)}</div>`;
            }

            // 原始响应
            if (result.rawResponse) {
                html += `<h4>原始响应:</h4>`;
                html += `<div class="result-json">${result.rawResponse}</div>`;
            }

            resultContainer.innerHTML = html;
        }

        // 显示性能指标
        function displayMetrics(service, metadata) {
            const metricsContainer = document.getElementById(`${service}Metrics`);
            
            const metrics = [
                { label: '响应时间', value: `${metadata.responseTime}ms`, key: 'responseTime' },
                { label: '订单数量', value: metadata.orderCount, key: 'orderCount' },
                { label: '输入长度', value: `${metadata.inputLength}字符`, key: 'inputLength' },
                { label: '输出长度', value: `${metadata.outputLength}字符`, key: 'outputLength' }
            ];

            let html = '';
            metrics.forEach(metric => {
                html += `
                    <div class="metric-item">
                        <div class="metric-value">${metric.value}</div>
                        <div class="metric-label">${metric.label}</div>
                    </div>
                `;
            });

            metricsContainer.innerHTML = html;
        }

        // 显示错误
        function displayError(service, errorMessage) {
            const resultContainer = document.getElementById(`${service}Result`);
            resultContainer.innerHTML = `<div class="error-message">测试失败: ${errorMessage}</div>`;
        }

        // 显示全局错误
        function showError(message) {
            alert(message); // 简单的错误显示，可以改为更友好的UI
        }

        // 生成对比结果
        function generateComparison() {
            if (!testState.results.gemini || !testState.results.kimi) {
                return;
            }

            const gemini = testState.results.gemini;
            const kimi = testState.results.kimi;

            let html = `
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>指标</th>
                            <th>Gemini 2.5 Flash</th>
                            <th>Kimi K2</th>
                            <th>胜出</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 响应时间对比
            const geminiTime = gemini.metadata?.responseTime || 0;
            const kimiTime = kimi.metadata?.responseTime || 0;
            const timeWinner = geminiTime < kimiTime ? 'Gemini' : 'Kimi';
            
            html += `
                <tr>
                    <td>响应时间</td>
                    <td class="${timeWinner === 'Gemini' ? 'winner' : 'loser'}">${geminiTime}ms</td>
                    <td class="${timeWinner === 'Kimi' ? 'winner' : 'loser'}">${kimiTime}ms</td>
                    <td class="winner">${timeWinner}</td>
                </tr>
            `;

            // 解析成功率对比
            const geminiSuccess = gemini.success ? 100 : 0;
            const kimiSuccess = kimi.success ? 100 : 0;
            const successWinner = geminiSuccess > kimiSuccess ? 'Gemini' : (kimiSuccess > geminiSuccess ? 'Kimi' : '平局');

            html += `
                <tr>
                    <td>解析成功率</td>
                    <td class="${successWinner === 'Gemini' ? 'winner' : (successWinner === '平局' ? '' : 'loser')}">${geminiSuccess}%</td>
                    <td class="${successWinner === 'Kimi' ? 'winner' : (successWinner === '平局' ? '' : 'loser')}">${kimiSuccess}%</td>
                    <td class="winner">${successWinner}</td>
                </tr>
            `;

            // 订单解析数量对比
            const geminiOrderCount = gemini.metadata?.orderCount || 0;
            const kimiOrderCount = kimi.metadata?.orderCount || 0;
            const countWinner = geminiOrderCount > kimiOrderCount ? 'Gemini' : (kimiOrderCount > geminiOrderCount ? 'Kimi' : '平局');

            html += `
                <tr>
                    <td>识别订单数量</td>
                    <td class="${countWinner === 'Gemini' ? 'winner' : (countWinner === '平局' ? '' : 'loser')}">${geminiOrderCount}个</td>
                    <td class="${countWinner === 'Kimi' ? 'winner' : (countWinner === '平局' ? '' : 'loser')}">${kimiOrderCount}个</td>
                    <td class="winner">${countWinner}</td>
                </tr>
            `;

            html += `
                    </tbody>
                </table>
            `;

            // 添加总结
            html += `
                <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                    <h4>📋 测试总结</h4>
                    <p><strong>测试时间:</strong> ${new Date().toLocaleString()}</p>
                    <p><strong>总测试时长:</strong> ${Date.now() - testState.startTime}ms</p>
                    <p><strong>输入文本长度:</strong> ${document.getElementById('orderText').value.length} 字符</p>
                </div>
            `;

            document.getElementById('comparisonContent').innerHTML = html;
            document.getElementById('comparisonSummary').style.display = 'block';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('LLM性能测试页面加载完成');
            
            // 检查服务可用性
            try {
                const geminiService = window.getGeminiService();
                const kimiService = window.getKimiService();
                console.log('Gemini服务状态:', geminiService.getStatus ? geminiService.getStatus() : '服务可用');
                console.log('Kimi服务状态:', kimiService.getStatus ? kimiService.getStatus() : '服务可用');
            } catch (error) {
                console.error('服务初始化检查失败:', error);
            }
        });
    </script>
</body>
</html>