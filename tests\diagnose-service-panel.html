<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务配置面板诊断工具</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .diagnostic-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .header {
            background: #9F299F;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .diagnostic-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background: #f9f9f9;
        }
        
        .diagnostic-title {
            color: #9F299F;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: #9F299F;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            background: #B84CB8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        
        .test-area {
            border: 2px dashed #9F299F;
            padding: 20px;
            margin: 20px 0;
            border-radius: 12px;
            background: rgba(159, 41, 159, 0.05);
        }
        
        /* 复制主应用的关键样式用于测试 */
        .panel {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            border-radius: 12px;
            border: 1px solid rgba(159, 41, 159, 0.2);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            overflow: hidden;
            transition: all 0.3s ease;
            padding: 8px 10px;
            margin-bottom: 6px;
            min-height: auto;
            height: auto;
            flex-shrink: 0;
        }
        
        .compact-card {
            padding: 8px 10px;
            margin-bottom: 6px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(16px);
            -webkit-backdrop-filter: blur(16px);
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(159, 41, 159, 0.2);
            min-height: auto;
            height: auto;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }
        
        .multi-select-dropdown {
            position: relative;
            width: 100%;
        }
        
        .multi-select-trigger {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            min-height: 40px;
        }
        
        .multi-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #9F299F;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-height: 250px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-8px);
            transition: all 0.2s ease-in-out;
        }
        
        .multi-select-options.show {
            display: block !important;
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }
        
        .multi-select-option {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.15s ease;
            border-bottom: 1px solid #eee;
        }
        
        .multi-select-option:hover {
            background-color: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <div class="header">
            <h1>🔍 服务配置面板诊断工具</h1>
            <p>实时检查和修复服务配置面板显示问题</p>
        </div>
        
        <div class="content">
            <!-- 测试区域 -->
            <div class="test-area">
                <h3>🎯 测试环境 - 模拟服务配置面板</h3>
                
                <!-- 模拟的服务配置面板 -->
                <div class="panel compact-card" data-panel="service-config" id="testServicePanel">
                    <div class="section-header">
                        <h3>⚙️ 服务配置</h3>
                    </div>
                    <div class="panel-content">
                        <div style="margin-bottom: 15px;">
                            <label>乘客人数:</label>
                            <input type="number" value="2" style="width: 80px; padding: 5px; margin-left: 10px;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label>语言要求:</label>
                            <div class="multi-select-dropdown" id="testLanguageDropdown">
                                <div class="multi-select-trigger" id="testLanguageTrigger">
                                    <span class="multi-select-text">请选择语言</span>
                                    <span class="multi-select-arrow">▼</span>
                                </div>
                                <div class="multi-select-options" id="testLanguageOptions">
                                    <div class="multi-select-option">
                                        <input type="checkbox" style="margin-right: 8px;"> 中文
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" style="margin-right: 8px;"> English
                                    </div>
                                    <div class="multi-select-option">
                                        <input type="checkbox" style="margin-right: 8px;"> Bahasa Malaysia
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label>价格:</label>
                            <input type="number" value="150" style="width: 100px; padding: 5px; margin-left: 10px;">
                            <span style="margin-left: 5px;">MYR</span>
                        </div>
                    </div>
                </div>
                
                <p><strong>状态：</strong><span id="panelStatus" class="status warning">等待检测</span></p>
            </div>
            
            <!-- 诊断工具 -->
            <div class="diagnostic-section">
                <div class="diagnostic-title">
                    🏥 1. 元素存在性和基础属性检查
                </div>
                <button class="btn" onclick="checkElementExistence()">检查元素是否存在</button>
                <button class="btn" onclick="checkBasicStyles()">检查基础样式</button>
                <button class="btn" onclick="checkPosition()">检查位置信息</button>
                <div id="elementResults" class="results"></div>
            </div>
            
            <div class="diagnostic-section">
                <div class="diagnostic-title">
                    🎭 2. 下拉菜单交互测试
                </div>
                <button class="btn" onclick="testDropdownInteraction()">模拟下拉菜单点击</button>
                <button class="btn" onclick="checkOverlap()">检查遮挡情况</button>
                <button class="btn-secondary" onclick="closeDropdown()">关闭下拉菜单</button>
                <div id="interactionResults" class="results"></div>
            </div>
            
            <div class="diagnostic-section">
                <div class="diagnostic-title">
                    🔧 3. 实时修复测试
                </div>
                <button class="btn" onclick="applyOpacityFix()">增加背景不透明度</button>
                <button class="btn" onclick="applyZIndexFix()">调整Z-Index层级</button>
                <button class="btn" onclick="applyBorderFix()">添加明显边框</button>
                <button class="btn-secondary" onclick="resetStyles()">重置样式</button>
                <div id="fixResults" class="results"></div>
            </div>
            
            <div class="diagnostic-section">
                <div class="diagnostic-title">
                    📊 4. 完整诊断报告
                </div>
                <button class="btn" onclick="generateFullReport()">生成完整报告</button>
                <button class="btn" onclick="exportReport()">导出报告</button>
                <div id="fullReport" class="results"></div>
            </div>
        </div>
    </div>

    <script>
        // Chrome MCP诊断脚本
        
        let originalStyles = {};
        
        // 1. 检查元素存在性
        function checkElementExistence() {
            const results = document.getElementById('elementResults');
            const servicePanel = document.getElementById('testServicePanel');
            const dropdown = document.getElementById('testLanguageDropdown');
            const trigger = document.getElementById('testLanguageTrigger');
            const options = document.getElementById('testLanguageOptions');
            
            let report = '=== 元素存在性检查 ===\\n';
            report += `服务配置面板: ${servicePanel ? '✅ 存在' : '❌ 不存在'}\\n`;
            report += `下拉菜单容器: ${dropdown ? '✅ 存在' : '❌ 不存在'}\\n`;
            report += `下拉菜单触发器: ${trigger ? '✅ 存在' : '❌ 不存在'}\\n`;
            report += `下拉菜单选项: ${options ? '✅ 存在' : '❌ 不存在'}\\n`;
            
            if (servicePanel) {
                report += '\\n=== 服务面板详细信息 ===\\n';
                report += `ID: ${servicePanel.id}\\n`;
                report += `类名: ${servicePanel.className}\\n`;
                report += `data-panel: ${servicePanel.getAttribute('data-panel')}\\n`;
            }
            
            results.textContent = report;
            updateStatus('检查完成', 'success');
        }
        
        // 2. 检查基础样式
        function checkBasicStyles() {
            const results = document.getElementById('elementResults');
            const servicePanel = document.getElementById('testServicePanel');
            
            if (!servicePanel) {
                results.textContent = '❌ 服务配置面板不存在';
                return;
            }
            
            const styles = getComputedStyle(servicePanel);
            let report = '=== 计算样式检查 ===\\n';
            report += `display: ${styles.display}\\n`;
            report += `visibility: ${styles.visibility}\\n`;
            report += `opacity: ${styles.opacity}\\n`;
            report += `position: ${styles.position}\\n`;
            report += `z-index: ${styles.zIndex}\\n`;
            report += `background: ${styles.background}\\n`;
            report += `width: ${styles.width}\\n`;
            report += `height: ${styles.height}\\n`;
            report += `transform: ${styles.transform}\\n`;
            
            // 检查问题
            let issues = [];
            if (parseFloat(styles.opacity) < 0.5) issues.push('透明度过低');
            if (styles.display === 'none') issues.push('display设为none');
            if (styles.visibility === 'hidden') issues.push('visibility设为hidden');
            
            if (issues.length > 0) {
                report += '\\n🚨 发现问题:\\n' + issues.map(issue => `- ${issue}`).join('\\n');
            } else {
                report += '\\n✅ 样式正常';
            }
            
            results.textContent = report;
        }
        
        // 3. 检查位置信息
        function checkPosition() {
            const results = document.getElementById('elementResults');
            const servicePanel = document.getElementById('testServicePanel');
            
            if (!servicePanel) {
                results.textContent = '❌ 服务配置面板不存在';
                return;
            }
            
            const rect = servicePanel.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            
            let report = '=== 位置信息检查 ===\\n';
            report += `位置: (${rect.left.toFixed(1)}, ${rect.top.toFixed(1)})\\n`;
            report += `尺寸: ${rect.width.toFixed(1)} × ${rect.height.toFixed(1)}\\n`;
            report += `视口尺寸: ${viewportWidth} × ${viewportHeight}\\n`;
            
            // 检查是否在视口内
            const inViewport = rect.top >= 0 && rect.left >= 0 && 
                              rect.bottom <= viewportHeight && rect.right <= viewportWidth;
            
            report += `\\n在视口内: ${inViewport ? '✅ 是' : '❌ 否'}\\n`;
            
            if (!inViewport) {
                if (rect.top < 0) report += '- 顶部超出视口\\n';
                if (rect.bottom > viewportHeight) report += '- 底部超出视口\\n';
                if (rect.left < 0) report += '- 左侧超出视口\\n';
                if (rect.right > viewportWidth) report += '- 右侧超出视口\\n';
            }
            
            results.textContent = report;
        }
        
        // 4. 测试下拉菜单交互
        function testDropdownInteraction() {
            const results = document.getElementById('interactionResults');
            const trigger = document.getElementById('testLanguageTrigger');
            const options = document.getElementById('testLanguageOptions');
            
            if (!trigger || !options) {
                results.textContent = '❌ 下拉菜单元素不存在';
                return;
            }
            
            let report = '=== 下拉菜单交互测试 ===\\n';
            report += '点击触发器...\\n';
            
            // 模拟点击
            trigger.click();
            options.classList.add('show');
            
            setTimeout(() => {
                const optionsStyles = getComputedStyle(options);
                report += `下拉菜单状态:\\n`;
                report += `- display: ${optionsStyles.display}\\n`;
                report += `- opacity: ${optionsStyles.opacity}\\n`;
                report += `- visibility: ${optionsStyles.visibility}\\n`;
                report += `- z-index: ${optionsStyles.zIndex}\\n`;
                
                const optionsRect = options.getBoundingClientRect();
                report += `\\n下拉菜单位置:\\n`;
                report += `- 位置: (${optionsRect.left.toFixed(1)}, ${optionsRect.top.toFixed(1)})\\n`;
                report += `- 尺寸: ${optionsRect.width.toFixed(1)} × ${optionsRect.height.toFixed(1)}\\n`;
                
                results.textContent = report;
            }, 100);
        }
        
        // 5. 检查遮挡情况
        function checkOverlap() {
            const results = document.getElementById('interactionResults');
            const servicePanel = document.getElementById('testServicePanel');
            const options = document.getElementById('testLanguageOptions');
            
            if (!servicePanel || !options) {
                results.textContent = '❌ 必要元素不存在';
                return;
            }
            
            const servicePanelRect = servicePanel.getBoundingClientRect();
            const optionsRect = options.getBoundingClientRect();
            
            let report = '=== 遮挡情况检查 ===\\n';
            report += `服务面板位置: (${servicePanelRect.left.toFixed(1)}, ${servicePanelRect.top.toFixed(1)})\\n`;
            report += `服务面板尺寸: ${servicePanelRect.width.toFixed(1)} × ${servicePanelRect.height.toFixed(1)}\\n`;
            report += `下拉菜单位置: (${optionsRect.left.toFixed(1)}, ${optionsRect.top.toFixed(1)})\\n`;
            report += `下拉菜单尺寸: ${optionsRect.width.toFixed(1)} × ${optionsRect.height.toFixed(1)}\\n`;
            
            // 检查重叠
            const isOverlapping = !(servicePanelRect.right < optionsRect.left || 
                                  servicePanelRect.left > optionsRect.right || 
                                  servicePanelRect.bottom < optionsRect.top || 
                                  servicePanelRect.top > optionsRect.bottom);
            
            report += `\\n🎯 重叠检测: ${isOverlapping ? '❌ 存在重叠' : '✅ 无重叠'}\\n`;
            
            if (isOverlapping) {
                // 计算重叠区域
                const overlapLeft = Math.max(servicePanelRect.left, optionsRect.left);
                const overlapRight = Math.min(servicePanelRect.right, optionsRect.right);
                const overlapTop = Math.max(servicePanelRect.top, optionsRect.top);
                const overlapBottom = Math.min(servicePanelRect.bottom, optionsRect.bottom);
                
                const overlapWidth = overlapRight - overlapLeft;
                const overlapHeight = overlapBottom - overlapTop;
                
                report += `重叠区域: ${overlapWidth.toFixed(1)} × ${overlapHeight.toFixed(1)}\\n`;
                report += `重叠百分比: ${((overlapWidth * overlapHeight) / (servicePanelRect.width * servicePanelRect.height) * 100).toFixed(1)}%`;
            }
            
            results.textContent = report;
        }
        
        // 6. 关闭下拉菜单
        function closeDropdown() {
            const options = document.getElementById('testLanguageOptions');
            if (options) {
                options.classList.remove('show');
            }
            
            const results = document.getElementById('interactionResults');
            results.textContent = '✅ 下拉菜单已关闭';
        }
        
        // 7. 应用透明度修复
        function applyOpacityFix() {
            const results = document.getElementById('fixResults');
            const servicePanel = document.getElementById('testServicePanel');
            
            if (!servicePanel) {
                results.textContent = '❌ 服务配置面板不存在';
                return;
            }
            
            // 保存原始样式
            if (!originalStyles.background) {
                originalStyles.background = servicePanel.style.background;
            }
            
            // 应用修复
            servicePanel.style.background = 'rgba(255, 255, 255, 0.95)';
            servicePanel.style.backdropFilter = 'blur(20px)';
            
            let report = '=== 透明度修复 ===\\n';
            report += '✅ 已增加背景不透明度至95%\\n';
            report += '✅ 已增强毛玻璃效果\\n';
            
            results.textContent = report;
            updateStatus('透明度修复已应用', 'success');
        }
        
        // 8. 应用Z-Index修复
        function applyZIndexFix() {
            const results = document.getElementById('fixResults');
            const servicePanel = document.getElementById('testServicePanel');
            
            if (!servicePanel) {
                results.textContent = '❌ 服务配置面板不存在';
                return;
            }
            
            // 保存原始样式
            if (!originalStyles.zIndex) {
                originalStyles.zIndex = servicePanel.style.zIndex;
            }
            
            // 应用修复
            servicePanel.style.position = 'relative';
            servicePanel.style.zIndex = '1001';
            
            let report = '=== Z-Index修复 ===\\n';
            report += '✅ 已设置position为relative\\n';
            report += '✅ 已设置z-index为1001（高于下拉菜单）\\n';
            
            results.textContent = report;
            updateStatus('Z-Index修复已应用', 'success');
        }
        
        // 9. 应用边框修复
        function applyBorderFix() {
            const results = document.getElementById('fixResults');
            const servicePanel = document.getElementById('testServicePanel');
            
            if (!servicePanel) {
                results.textContent = '❌ 服务配置面板不存在';
                return;
            }
            
            // 保存原始样式
            if (!originalStyles.border) {
                originalStyles.border = servicePanel.style.border;
                originalStyles.boxShadow = servicePanel.style.boxShadow;
            }
            
            // 应用修复
            servicePanel.style.border = '2px solid #9F299F';
            servicePanel.style.boxShadow = '0 4px 20px rgba(159, 41, 159, 0.3)';
            
            let report = '=== 边框修复 ===\\n';
            report += '✅ 已添加明显的紫色边框\\n';
            report += '✅ 已添加彩色阴影效果\\n';
            
            results.textContent = report;
            updateStatus('边框修复已应用', 'success');
        }
        
        // 10. 重置样式
        function resetStyles() {
            const results = document.getElementById('fixResults');
            const servicePanel = document.getElementById('testServicePanel');
            
            if (!servicePanel) {
                results.textContent = '❌ 服务配置面板不存在';
                return;
            }
            
            // 恢复原始样式
            Object.keys(originalStyles).forEach(property => {
                servicePanel.style[property] = originalStyles[property] || '';
            });
            
            originalStyles = {};
            
            let report = '=== 样式重置 ===\\n';
            report += '✅ 已恢复所有原始样式\\n';
            
            results.textContent = report;
            updateStatus('样式已重置', 'warning');
        }
        
        // 11. 生成完整报告
        function generateFullReport() {
            const results = document.getElementById('fullReport');
            const servicePanel = document.getElementById('testServicePanel');
            
            let report = '📋 === 完整诊断报告 ===\\n';
            report += `生成时间: ${new Date().toLocaleString()}\\n\\n`;
            
            // 基础检查
            if (servicePanel) {
                const styles = getComputedStyle(servicePanel);
                const rect = servicePanel.getBoundingClientRect();
                
                report += '🔍 基础信息:\\n';
                report += `- 元素存在: ✅\\n`;
                report += `- 显示状态: ${styles.display}\\n`;
                report += `- 可见性: ${styles.visibility}\\n`;
                report += `- 透明度: ${styles.opacity}\\n`;
                report += `- 位置: ${styles.position}\\n`;
                report += `- Z-Index: ${styles.zIndex}\\n`;
                
                report += '\\n📐 尺寸位置:\\n';
                report += `- 坐标: (${rect.left.toFixed(1)}, ${rect.top.toFixed(1)})\\n`;
                report += `- 尺寸: ${rect.width.toFixed(1)} × ${rect.height.toFixed(1)}\\n`;
                
                // 问题诊断
                let issues = [];
                if (parseFloat(styles.opacity) < 0.8) issues.push('透明度偏低');
                if (styles.zIndex === 'auto' || parseInt(styles.zIndex) < 1000) issues.push('Z-Index可能不足');
                if (rect.width === 0 || rect.height === 0) issues.push('尺寸异常');
                
                report += '\\n🚨 潜在问题:\\n';
                if (issues.length === 0) {
                    report += '- 未发现明显问题 ✅\\n';
                } else {
                    issues.forEach(issue => {
                        report += `- ${issue}\\n`;
                    });
                }
                
                // 建议修复方案
                report += '\\n💡 建议修复方案:\\n';
                if (parseFloat(styles.opacity) < 0.9) {
                    report += '- 增加背景不透明度到95%以上\\n';
                }
                if (styles.zIndex === 'auto' || parseInt(styles.zIndex) < 1000) {
                    report += '- 设置z-index为1001或更高\\n';
                }
                report += '- 添加明显边框以提高可见性\\n';
                report += '- 确保下拉菜单使用智能定位避免遮挡\\n';
                
            } else {
                report += '❌ 服务配置面板不存在\\n';
            }
            
            results.textContent = report;
        }
        
        // 12. 导出报告
        function exportReport() {
            const report = document.getElementById('fullReport').textContent;
            if (!report) {
                alert('请先生成完整报告');
                return;
            }
            
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `service-panel-diagnostic-${Date.now()}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            alert('诊断报告已导出');
        }
        
        // 更新状态显示
        function updateStatus(message, type) {
            const statusElement = document.getElementById('panelStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 自动执行基础检查
            setTimeout(() => {
                checkElementExistence();
                checkBasicStyles();
                checkPosition();
            }, 500);
            
            // 添加下拉菜单交互
            const trigger = document.getElementById('testLanguageTrigger');
            const options = document.getElementById('testLanguageOptions');
            
            if (trigger && options) {
                trigger.addEventListener('click', function() {
                    options.classList.toggle('show');
                });
                
                // 点击外部关闭
                document.addEventListener('click', function(e) {
                    if (!document.getElementById('testLanguageDropdown').contains(e.target)) {
                        options.classList.remove('show');
                    }
                });
            }
        });
    </script>
</body>
</html>