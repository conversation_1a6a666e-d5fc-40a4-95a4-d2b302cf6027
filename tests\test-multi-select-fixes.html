<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选组件修复功能测试</title>
    
    <!-- CSS基础样式 -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/components/forms.css">
    
    <style>
        body {
            font-family: var(--font-family);
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: var(--spacing-8);
            margin: 0;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-card);
            padding: var(--spacing-8);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-card);
        }
        
        .test-section {
            margin-bottom: var(--spacing-8);
            padding: var(--spacing-6);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
        }
        
        .test-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: var(--spacing-4);
        }
        
        .test-description {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-6);
            line-height: var(--line-height-relaxed);
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: var(--spacing-3) var(--spacing-6);
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: var(--font-size-sm);
        }
        
        .test-result {
            margin-top: var(--spacing-4);
            padding: var(--spacing-3);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
        }
        
        .test-result.success {
            background: var(--color-success-light);
            color: var(--color-success);
            border: 1px solid var(--color-success);
        }
        
        .test-result.error {
            background: var(--color-error-light);
            color: var(--color-error);
            border: 1px solid var(--color-error);
        }
        
        .test-info {
            background: var(--color-info-light);
            color: var(--color-info);
            border: 1px solid var(--color-info);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">切换主题</button>
    
    <div class="test-container">
        <h1>多选组件修复功能测试</h1>
        <p>测试修复的两个主要问题：自动关闭功能和颜色同步</p>
        
        <!-- 测试1: 自动关闭功能 -->
        <div class="test-section">
            <div class="test-title">测试1: 自动关闭功能</div>
            <div class="test-description">
                配置了不同的自动关闭选项，测试选择后是否按预期关闭
            </div>
            
            <div class="form-group">
                <label>多选模式 (不自动关闭)</label>
                <div class="multi-select-dropdown" id="multiSelectTest1">
                    <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                        <span class="multi-select-text placeholder">选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options">
                        <!-- 动态加载选项 -->
                    </div>
                    <select multiple style="display: none;">
                        <option value="1">English</option>
                        <option value="2">中文</option>
                        <option value="3">Malay</option>
                        <option value="4">Tamil</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label>单选模式 (选择后自动关闭)</label>
                <div class="multi-select-dropdown" id="singleSelectTest">
                    <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                        <span class="multi-select-text placeholder">选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options">
                        <!-- 动态加载选项 -->
                    </div>
                    <select multiple style="display: none;">
                        <option value="1">English</option>
                        <option value="2">中文</option>
                        <option value="3">Malay</option>
                        <option value="4">Tamil</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label>强制自动关闭模式</label>
                <div class="multi-select-dropdown" id="autoCloseTest">
                    <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                        <span class="multi-select-text placeholder">选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options">
                        <!-- 动态加载选项 -->
                    </div>
                    <select multiple style="display: none;">
                        <option value="1">English</option>
                        <option value="2">中文</option>
                        <option value="3">Malay</option>
                        <option value="4">Tamil</option>
                    </select>
                </div>
            </div>
            
            <div id="autoCloseTestResult" class="test-result test-info">
                请测试不同模式的自动关闭行为
            </div>
        </div>
        
        <!-- 测试2: 颜色同步 -->
        <div class="test-section">
            <div class="test-title">测试2: 颜色同步</div>
            <div class="test-description">
                测试主题切换时，多选组件的颜色是否正确同步
            </div>
            
            <div class="form-group">
                <label>主题适配测试</label>
                <div class="multi-select-dropdown" id="colorSyncTest">
                    <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                        <span class="multi-select-text placeholder">选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options">
                        <!-- 动态加载选项 -->
                    </div>
                    <select multiple style="display: none;">
                        <option value="1">English</option>
                        <option value="2">中文</option>
                        <option value="3">Malay</option>
                        <option value="4">Tamil</option>
                    </select>
                </div>
            </div>
            
            <div id="colorSyncTestResult" class="test-result test-info">
                请切换主题测试颜色同步效果
            </div>
        </div>
        
        <!-- 测试3: Select元素隐藏 -->
        <div class="test-section">
            <div class="test-title">测试3: Select元素隐藏</div>
            <div class="test-description">
                验证原生select元素是否被正确隐藏，不再出现重复显示
            </div>
            
            <div class="form-group">
                <label>Select隐藏测试</label>
                <div class="multi-select-dropdown" id="selectHideTest">
                    <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                        <span class="multi-select-text placeholder">选择语言</span>
                        <span class="multi-select-arrow">▼</span>
                    </div>
                    <div class="multi-select-options">
                        <!-- 动态加载选项 -->
                    </div>
                    <select multiple>
                        <option value="1">English</option>
                        <option value="2">中文</option>
                        <option value="3">Malay</option>
                        <option value="4">Tamil</option>
                    </select>
                </div>
            </div>
            
            <div id="selectHideTestResult" class="test-result test-info">
                检查原生select元素是否被正确隐藏
            </div>
        </div>
    </div>
    
    <!-- 加载脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/multi-select-dropdown.js"></script>
    
    <script>
        // 主题切换功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? '' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            // 测试颜色同步
            setTimeout(() => {
                checkColorSync();
            }, 100);
        }
        
        // 初始化测试
        let multiSelectTest1, singleSelectTest, autoCloseTest, colorSyncTest, selectHideTest;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化多选组件实例
            multiSelectTest1 = new MultiSelectDropdown('multiSelectTest1', {
                placeholder: '选择多个语言',
                maxDisplayItems: 3,
                closeOnSelect: false,
                autoClose: false,
                maxSelection: null
            });
            
            singleSelectTest = new MultiSelectDropdown('singleSelectTest', {
                placeholder: '选择一个语言',
                maxDisplayItems: 1,
                closeOnSelect: true,
                autoClose: false,
                maxSelection: 1
            });
            
            autoCloseTest = new MultiSelectDropdown('autoCloseTest', {
                placeholder: '选择语言',
                maxDisplayItems: 2,
                closeOnSelect: false,
                autoClose: true,
                maxSelection: null
            });
            
            colorSyncTest = new MultiSelectDropdown('colorSyncTest', {
                placeholder: '选择语言',
                maxDisplayItems: 3,
                closeOnSelect: false,
                autoClose: false,
                maxSelection: null
            });
            
            selectHideTest = new MultiSelectDropdown('selectHideTest', {
                placeholder: '选择语言',
                maxDisplayItems: 3,
                closeOnSelect: false,
                autoClose: false,
                maxSelection: null
            });
            
            // 检查select元素隐藏状态
            setTimeout(() => {
                checkSelectHidden();
            }, 500);
            
            // 设置自动关闭测试监听
            setupAutoCloseTests();
        });
        
        // 检查自动关闭功能
        function setupAutoCloseTests() {
            const testInstances = [
                { instance: multiSelectTest1, name: '多选模式', expected: '不应自动关闭' },
                { instance: singleSelectTest, name: '单选模式', expected: '选择后应自动关闭' },
                { instance: autoCloseTest, name: '强制自动关闭', expected: '任何选择都应自动关闭' }
            ];
            
            testInstances.forEach(test => {
                const container = test.instance.container;
                const optionsContainer = test.instance.optionsContainer;
                
                // 监听选项变化
                container.addEventListener('change', () => {
                    setTimeout(() => {
                        const isOpen = optionsContainer.classList.contains('show');
                        updateAutoCloseResult(test.name, test.expected, !isOpen);
                    }, 200);
                });
            });
        }
        
        function updateAutoCloseResult(testName, expected, passed) {
            const resultDiv = document.getElementById('autoCloseTestResult');
            if (passed) {
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `✓ ${testName}: ${expected} - 测试通过`;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ ${testName}: ${expected} - 测试失败`;
            }
        }
        
        // 检查颜色同步
        function checkColorSync() {
            const trigger = document.querySelector('#colorSyncTest .multi-select-trigger');
            const options = document.querySelector('#colorSyncTest .multi-select-options');
            
            if (!trigger || !options) return;
            
            const triggerStyle = getComputedStyle(trigger);
            const optionsStyle = getComputedStyle(options);
            
            const resultDiv = document.getElementById('colorSyncTestResult');
            
            // 检查CSS变量是否正确应用
            const expectedBg = getComputedStyle(document.documentElement).getPropertyValue('--bg-card').trim();
            const actualBg = triggerStyle.backgroundColor;
            
            if (actualBg && expectedBg) {
                resultDiv.className = 'test-result success';
                resultDiv.textContent = '✓ 颜色同步测试通过 - CSS变量正确应用';
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = '✗ 颜色同步测试失败 - CSS变量未正确应用';
            }
        }
        
        // 检查select元素隐藏状态
        function checkSelectHidden() {
            const selectElements = document.querySelectorAll('.multi-select-dropdown select');
            let allHidden = true;
            let hiddenCount = 0;
            
            selectElements.forEach(select => {
                const style = getComputedStyle(select);
                const isHidden = style.display === 'none' || style.visibility === 'hidden' || select.hidden;
                
                if (isHidden) {
                    hiddenCount++;
                } else {
                    allHidden = false;
                }
            });
            
            const resultDiv = document.getElementById('selectHideTestResult');
            
            if (allHidden && selectElements.length > 0) {
                resultDiv.className = 'test-result success';
                resultDiv.textContent = `✓ Select隐藏测试通过 - 所有${selectElements.length}个select元素已正确隐藏`;
            } else {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = `✗ Select隐藏测试失败 - ${hiddenCount}/${selectElements.length}个select元素被隐藏`;
            }
        }
        
        // 初始颜色同步检查
        setTimeout(() => {
            checkColorSync();
        }, 1000);
    </script>
</body>
</html>