/**
 * @OTA_PROCESSOR 多订单批量处理器
 * 🏷️ 标签: @OTA_MULTI_ORDER_PROCESSOR
 * 📝 说明: 负责订单解析和API调用，处理批量订单创建
 * ⚠️ 警告: 已注册，请勿重复开发
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.MultiOrderProcessor) {
    console.log('多订单批量处理器已存在，跳过重复加载');
} else {

/**
 * 多订单批量处理器类
 * 负责订单的解析、验证和API调用
 */
class MultiOrderProcessor {
    constructor(config = {}) {
        this.config = {
            maxOrdersPerBatch: config.maxOrdersPerBatch || 5,
            batchDelay: config.batchDelay || 800,
            maxRetries: config.maxRetries || 3,
            timeout: config.timeout || 30000,
            ...config
        };
        
        this.logger = this.getLogger();
        this.batchProgress = {
            total: 0,
            completed: 0,
            failed: 0,
            isRunning: false,
            startTime: null,
            processingOrder: null
        };
    }

    /**
     * 获取日志服务
     * @returns {Object} 日志服务实例
     */
    getLogger() {
        return window.getLogger?.() || {
            log: console.log.bind(console),
            logError: console.error.bind(console)
        };
    }

    /**
     * 处理单个订单（AI解析）
     * @param {Object} orderData - 订单数据
     * @param {number} index - 订单索引
     * @returns {Promise<Object>} 处理结果
     */
    async processOrder(orderData, index) {
        this.logger?.log(`开始处理订单 ${index + 1}`, 'info');

        try {
            if (!orderData) {
                throw new Error(`订单 ${index + 1} 数据为空`);
            }

            const orderText = orderData.rawText || JSON.stringify(orderData);
            if (!orderText.trim()) {
                throw new Error(`订单 ${index + 1} 文本为空`);
            }
            
            // 调用Gemini AI分析（增加重试机制）
            const geminiService = this.getGeminiService();
            if (!geminiService || !geminiService.isAvailable()) {
                throw new Error('Gemini AI服务不可用');
            }

            let result = null;
            let retryCount = 0;
            
            while (retryCount < this.config.maxRetries) {
                try {
                    result = await geminiService.parseOrder(orderText);
                    break; // 成功则退出重试循环
                } catch (apiError) {
                    retryCount++;
                    this.logger?.logError(`订单 ${index + 1} API调用失败（尝试 ${retryCount}/${this.config.maxRetries}）`, apiError);
                    
                    if (retryCount >= this.config.maxRetries) {
                        throw new Error(`API调用失败，已重试 ${this.config.maxRetries} 次: ${apiError.message}`);
                    }
                    
                    // 指数退避延迟
                    const delay = Math.pow(2, retryCount) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
            
            if (!result) {
                throw new Error('无法获取解析结果');
            }
            
            // 验证结果完整性
            if (!result.success) {
                throw new Error(result.message || '订单解析失败');
            }
            
            if (!result.data) {
                throw new Error('解析结果缺少数据');
            }
            
            // 应用字段映射和验证
            const processedData = this.applyFieldMappingAndValidation(result.data);
            
            this.logger?.log(`订单 ${index + 1} 处理完成`, 'success', {
                confidence: result.confidence,
                retryCount: retryCount,
                dataKeys: Object.keys(processedData || {})
            });

            return {
                success: true,
                data: processedData,
                confidence: result.confidence || 0,
                retryCount: retryCount,
                timestamp: Date.now()
            };

        } catch (error) {
            this.logger?.logError(`处理订单 ${index + 1} 失败: ${error.message}`, error);
            
            return {
                success: false,
                error: {
                    message: error.message,
                    type: this.classifyErrorType(error),
                    stack: error.stack
                },
                timestamp: Date.now()
            };
        }
    }

    /**
     * 批量处理所有订单
     * @param {Array} orders - 订单数组
     * @returns {Promise<Object>} 批量处理结果
     */
    async processAllOrders(orders) {
        // 检查是否已在运行
        if (this.batchProgress.isRunning) {
            this.logger?.log('⚠️ 批量处理已在运行中，跳过重复执行', 'warn');
            return { success: false, reason: '批量处理已在运行中' };
        }

        this.logger?.log('开始批量处理所有订单', 'info');

        const total = orders.length;
        if (total === 0) {
            this.logger?.log('没有订单需要处理', 'info');
            return { success: true, results: [] };
        }

        // 初始化批量进度
        this.batchProgress = {
            total,
            completed: 0,
            failed: 0,
            isRunning: true,
            startTime: Date.now(),
            processingOrder: null
        };

        const results = [];

        try {
            for (let i = 0; i < total; i++) {
                // 检查是否应该停止
                if (!this.batchProgress.isRunning) {
                    this.logger?.log('批量处理被用户中断', 'info');
                    break;
                }

                // 设置当前处理订单
                this.batchProgress.processingOrder = i;
                
                try {
                    const result = await this.processOrder(orders[i], i);
                    results.push(result);
                    
                    if (result.success) {
                        this.batchProgress.completed++;
                    } else {
                        this.batchProgress.failed++;
                    }
                } catch (error) {
                    this.logger?.logError(`批量处理订单 ${i + 1} 失败`, error);
                    this.batchProgress.failed++;
                    results.push({
                        success: false,
                        error: { message: error.message },
                        timestamp: Date.now()
                    });
                }
                
                // 添加延迟避免API过载
                if (i < total - 1 && this.batchProgress.isRunning) {
                    await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
                }
            }
        } catch (unexpectedError) {
            this.logger?.logError('批量处理出现未预期错误', unexpectedError);
        } finally {
            // 确保状态正确重置
            this.batchProgress.isRunning = false;
            this.batchProgress.processingOrder = null;
            
            const duration = Date.now() - this.batchProgress.startTime;
            this.logger?.log(`批量处理完成，成功: ${this.batchProgress.completed}, 失败: ${this.batchProgress.failed}, 耗时: ${duration}ms`, 'success');
        }

        return {
            success: true,
            results,
            summary: {
                total: this.batchProgress.total,
                completed: this.batchProgress.completed,
                failed: this.batchProgress.failed,
                duration: Date.now() - this.batchProgress.startTime
            }
        };
    }

    /**
     * 创建单个订单
     * @param {Object} orderData - 订单数据
     * @param {number} index - 订单索引
     * @returns {Promise<Object>} 创建结果
     */
    async createSingleOrder(orderData, index) {
        console.group(`🔍 多订单数据流追踪 - 第11步：createSingleOrder(${index})`);
        console.log('订单索引:', index);
        console.log('订单数据:', orderData);

        try {
            const apiService = this.getApiService();
            if (!apiService) {
                throw new Error('API服务不可用');
            }

            this.logger?.log(`开始创建订单 ${index + 1}`, 'info');
            
            // 🔧 使用api-service的统一预处理逻辑（包含字段映射）
            console.log('开始数据预处理...');
            const preprocessedData = apiService.preprocessOrderData(orderData);
            console.log('预处理后的数据:', preprocessedData);
            
            // 验证订单数据
            console.log('开始数据验证...');
            const validation = apiService.validateOrderData(preprocessedData);
            console.log('验证结果:', validation);
            
            if (!validation.isValid) {
                console.error('❌ 数据验证失败:', validation.errors);
                throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
            }

            console.group('🔍 多订单数据流追踪 - 第12步：调用GoMyHire API');
            console.log('即将调用apiService.createOrder...');
            console.log('发送的数据:', preprocessedData);
            const result = await apiService.createOrder(preprocessedData);
            console.log('API返回结果:', result);
            console.groupEnd();
            
            if (result.success) {
                const orderId = result.data?.id || result.data?.order_id || result.id || 'N/A';
                console.log('✅ 订单创建成功!', { orderId: orderId });
                this.logger?.log(`订单 ${index + 1} 创建成功`, 'success', { orderId });
                
                // 记录到历史
                try {
                    const historyManager = window.OTA?.orderHistoryManager || window.orderHistoryManager;
                    if (historyManager && typeof historyManager.addOrder === 'function') {
                        historyManager.addOrder(preprocessedData, orderId, result);
                        this.logger?.log('订单已记录到历史', 'info', { orderId });
                    }
                } catch (historyError) {
                    this.logger?.logError('记录订单历史失败', historyError);
                }

                return {
                    success: true,
                    orderId: orderId,
                    data: result.data,
                    timestamp: Date.now()
                };
            } else {
                const errorMessage = result.message || result.error || '订单创建失败';
                console.error('❌ API返回失败:', errorMessage);
                throw new Error(errorMessage);
            }
        } catch (error) {
            console.error('❌ 创建订单异常:', error);
            this.logger?.logError(`创建订单 ${index + 1} 异常`, error);
            
            return {
                success: false,
                error: {
                    message: error.message,
                    type: this.classifyErrorType(error)
                },
                timestamp: Date.now()
            };
        } finally {
            console.groupEnd();
        }
    }

    /**
     * 批量创建选中的订单
     * @param {Array} selectedOrders - 选中的订单数组
     * @returns {Promise<Object>} 批量创建结果
     */
    async createSelectedOrders(selectedOrders) {
        console.group('🔍 多订单数据流追踪 - 第9步：批量创建订单');
        console.log('选中的订单数量:', selectedOrders.length);
        
        if (selectedOrders.length === 0) {
            console.warn('❌ 没有选中的订单');
            console.groupEnd();
            return { success: false, reason: '没有选中的订单' };
        }

        this.logger?.log(`开始批量创建 ${selectedOrders.length} 个选中订单`, 'info');

        let successCount = 0;
        let failedCount = 0;
        const results = [];

        for (let i = 0; i < selectedOrders.length; i++) {
            const order = selectedOrders[i];
            
            console.group(`🔍 多订单数据流追踪 - 第10步：创建订单${i + 1}`);
            console.log('订单索引:', i);
            
            try {
                console.log('开始创建单个订单...');
                const result = await this.createSingleOrder(order, i);
                results.push(result);
                
                if (result.success) {
                    successCount++;
                    console.log('✅ 订单创建成功');
                } else {
                    failedCount++;
                    console.error('❌ 订单创建失败:', result.error);
                }
            } catch (error) {
                console.error('❌ 订单创建失败:', error);
                this.logger?.logError(`创建订单 ${i + 1} 失败`, error);
                failedCount++;
                results.push({
                    success: false,
                    error: { message: error.message },
                    timestamp: Date.now()
                });
            }
            console.groupEnd();
            
            // 添加延迟避免API过载
            if (i < selectedOrders.length - 1) {
                await new Promise(resolve => setTimeout(resolve, this.config.batchDelay));
            }
        }
        console.groupEnd();

        const summary = {
            total: selectedOrders.length,
            success: successCount,
            failed: failedCount
        };

        this.logger?.log(`批量创建完成！成功: ${successCount}, 失败: ${failedCount}`, 'info');

        return {
            success: failedCount === 0,
            results,
            summary
        };
    }

    /**
     * 应用字段映射和验证
     * @param {Object} orderData - 原始订单数据
     * @returns {Object} 处理后的订单数据
     */
    applyFieldMappingAndValidation(orderData) {
        // 获取数据转换器
        const transformer = this.getTransformer();
        if (transformer) {
            return transformer.transformOrderData(orderData);
        }

        // 如果没有转换器，使用基本的字段映射
        return this.basicFieldMapping(orderData);
    }

    /**
     * 基本字段映射（备用方案）
     * @param {Object} orderData - 原始订单数据
     * @returns {Object} 映射后的订单数据
     */
    basicFieldMapping(orderData) {
        const config = window.OTA?.FieldMappingConfig || window.FIELD_MAPPING_CONFIG;
        if (!config) {
            return orderData;
        }

        const mappedData = { ...orderData };

        // 应用备用字段映射
        Object.entries(config.ALTERNATIVE_FIELDS).forEach(([primaryField, alternatives]) => {
            if (!mappedData[primaryField]) {
                for (const altField of alternatives) {
                    if (mappedData[altField]) {
                        mappedData[primaryField] = mappedData[altField];
                        break;
                    }
                }
            }
        });

        return mappedData;
    }

    /**
     * 分类错误类型
     * @param {Error} error - 错误对象
     * @returns {string} 错误类型
     */
    classifyErrorType(error) {
        const message = error.message.toLowerCase();
        
        if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
            return 'network';
        } else if (message.includes('api') || message.includes('service')) {
            return 'api';
        } else if (message.includes('validation') || message.includes('required') || message.includes('invalid')) {
            return 'validation';
        } else if (message.includes('range') || message.includes('index')) {
            return 'range';
        } else {
            return 'unknown';
        }
    }

    /**
     * 获取Gemini服务实例
     * @returns {Object} Gemini服务实例
     */
    getGeminiService() {
        return window.getGeminiService?.() || null;
    }

    /**
     * 获取API服务实例
     * @returns {Object} API服务实例
     */
    getApiService() {
        return window.getApiService?.() || window.getService?.('apiService') || window.getAPIService?.();
    }

    /**
     * 获取数据转换器实例
     * @returns {Object} 数据转换器实例
     */
    getTransformer() {
        return window.OTA?.MultiOrderTransformer ? new window.OTA.MultiOrderTransformer() : null;
    }

    /**
     * 停止批量处理
     */
    stopBatchProcessing() {
        this.batchProgress.isRunning = false;
        this.logger?.log('批量处理已停止', 'info');
    }

    /**
     * 获取批量处理进度
     * @returns {Object} 进度信息
     */
    getBatchProgress() {
        return { ...this.batchProgress };
    }
}

// 导出批量处理器
window.OTA = window.OTA || {};
window.OTA.MultiOrderProcessor = MultiOrderProcessor;

// 向后兼容
window.MultiOrderProcessor = MultiOrderProcessor;

console.log('✅ 多订单批量处理器已加载');

// 结束防重复加载检查
}
