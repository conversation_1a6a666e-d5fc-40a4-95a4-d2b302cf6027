/**
 * 表单管理器模块
 * 负责表单数据的填充、收集、验证和处理
 * 支持多种字段类型和智能默认值设置
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};
window.OTA.managers = window.OTA.managers || {};

(function() {
    'use strict';

    // 获取依赖模块 - 使用统一的服务定位器

    /**
     * 表单管理器类
     * 负责表单相关的所有操作
     */
    class FormManager {
        constructor(elements) {
            this.elements = elements;
            this.languagesDropdown = null;
        }

        /**
         * 初始化表单管理器
         */
        init() {
            this.setupRealtimeValidation();
            this.initializePriceConversionListeners();
            this.setDefaultValues();
            this.initAutoResizeTextarea(); // 初始化自适应高度文本框

            // 确保系统数据已加载并填充表单选项
            setTimeout(() => {
                this.populateFormOptions();
                // 修复：将自定义组件的初始化移到这里，确保在标准组件之后加载
                this.initCustomComponents();
                // 初始化价格字段权限控制
                this.initializePriceFieldPermissions();
            }, 100);

            getLogger().log('表单管理器初始化完成', 'success');
        }

        /**
         * 初始化自定义UI组件，如多选下拉菜单
         */
        initCustomComponents() {
            // 启动新的语言下拉菜单初始化流程
            this.initLanguagesDropdown();
        }

        /**
         * 初始化价格字段权限控制
         */
        initializePriceFieldPermissions() {
            try {
                const apiService = getApiService();
                const permissions = apiService.checkPriceFieldPermissions();

                // 获取价格字段元素
                const otaPriceGroup = document.getElementById('otaPriceGroup');
                const driverFeeGroup = document.getElementById('driverFeeGroup');

                // 根据权限显示/隐藏字段
                if (otaPriceGroup) {
                    otaPriceGroup.style.display = permissions.canViewOtaPrice ? 'flex' : 'none';
                }

                if (driverFeeGroup) {
                    driverFeeGroup.style.display = permissions.canViewDriverFee ? 'flex' : 'none';
                }

                getLogger().log('价格字段权限控制初始化完成', 'success', {
                    canViewOtaPrice: permissions.canViewOtaPrice,
                    canViewDriverFee: permissions.canViewDriverFee
                });

            } catch (error) {
                getLogger().logError('初始化价格字段权限控制失败', error);
            }
        }

        /**
         * 初始化语言选择器（简化版本）
         */
        async initLanguagesDropdown() {
            const logger = getLogger();

            try {
                logger.log('🔧 [FormManager] 初始化原生语言选择器', 'info');

                // 原生select元素已经在HTML中定义，无需额外初始化
                if (this.elements.languagesIdArray) {
                    logger.log('✅ [FormManager] 语言选择器已就绪', 'success');
                    return true;
                } else {
                    throw new Error('语言选择器元素未找到');
                }

            } catch (error) {
                logger.logError('💥 [FormManager] 初始化语言选择器失败', error);
                return false;
            }
        }



        /**
         * 获取语言数据 - 使用统一数据管理器
         * @returns {Promise<Array>} 语言数据数组
         */
        async getLanguageData() {
            try {
                // 🔧 优先使用统一数据管理器
                const dataManager = window.OTA?.unifiedDataManager || window.unifiedDataManager;
                
                if (dataManager && typeof dataManager.getData === 'function') {
                    return await dataManager.getData('languages', {
                        useCache: true,
                        fallbackToCache: true
                    });
                }

                // 降级到原有逻辑
                return this.getLegacyLanguageData();
                
            } catch (error) {
                const logger = getLogger();
                logger.logError('[FormManager] 统一数据管理器获取语言数据失败，使用降级方案', error);
                
                return this.getLegacyLanguageData();
            }
        }

        /**
         * 降级的语言数据获取方法
         * @returns {Array} 语言数据数组
         */
        getLegacyLanguageData() {
            let systemData = getAppState().get('systemData');
            const apiService = getApiService();

            // 强制数据同步：优先使用ApiService的静态数据
            if (!systemData || !systemData.languages || systemData.languages.length === 0) {
                if (apiService.staticData && apiService.staticData.languages && apiService.staticData.languages.length > 0) {
                    getAppState().setSystemData(apiService.staticData);
                    systemData = apiService.staticData;
                } else {
                    // 使用语言管理器的fallback数据
                    const languageManager = getLanguageManager();
                    const fallbackLanguages = languageManager.getLanguagesSync({ enabledOnly: true });
                    const fallbackData = { languages: fallbackLanguages };
                    getAppState().setSystemData(fallbackData);
                    systemData = fallbackData;
                }
            }

            return systemData?.languages || [];
        }



        /**
         * 设置表单的默认值
         */
        setDefaultValues() {
            // 延迟执行，确保用户认证状态已经加载
            setTimeout(() => {
                const defaultUserId = getApiService().getDefaultBackendUserId();
                if (defaultUserId && this.elements.inchargeByBackendUserId) {
                    this.elements.inchargeByBackendUserId.value = defaultUserId;
                    getLogger().log('已设置默认负责人', 'info', { userId: defaultUserId });
                } else {
                    getLogger().log('无法设置默认负责人', 'warning', { 
                        hasUserId: !!defaultUserId,
                        hasElement: !!this.elements.inchargeByBackendUserId
                    });
                }
            }, 500);
        }

        /**
         * 填充表单选项
         */
        populateFormOptions() {
            try {
                // 获取系统数据，如果AppState中没有，则使用ApiService的静态数据作为降级
                let systemData = getAppState().get('systemData') || {};
                
                // 检查是否需要重新填充DOM选项
                const isDataIncomplete = !systemData.backendUsers || systemData.backendUsers.length === 0;
                
                if (isDataIncomplete) {
                    getLogger().log('检测到DOM未被填充，需要从AppState重新填充选项', 'warning', {
                        backendUsersCount: (systemData.backendUsers || []).length,
                        carTypesCount: (systemData.carTypes || []).length,
                        subCategoriesCount: (systemData.subCategories || []).length
                    });
                    
                    // 尝试从ApiService获取静态数据作为降级方案
                    const apiService = getApiService();
                    if (apiService && apiService.staticData) {
                        systemData = apiService.staticData;
                        getAppState().setSystemData(systemData);
                        getLogger().log('已从ApiService加载静态数据作为降级方案', 'info');
                    }
                }

                // 安全地填充各种下拉选项
                this.populateSelect(this.elements.subCategoryId, systemData.subCategories, 'id', 'name', 'form.selectServiceType');
                this.populateSelect(this.elements.carTypeId, systemData.carTypes, 'id', 'name', 'form.selectCarType');
                this.populateSelect(this.elements.inchargeByBackendUserId, systemData.backendUsers, 'id', 'name');
                this.populateSelect(this.elements.drivingRegionId, systemData.drivingRegions, 'id', 'name', 'form.selectDrivingRegion');
                // this.populateSelect(this.elements.languagesIdArray, systemData.languages, 'id', 'name', 'form.selectLanguages'); // 由 initCustomComponents 处理

                // 设置默认值
                const defaultUserId = getApiService().getDefaultBackendUserId();
                if (defaultUserId && this.elements.inchargeByBackendUserId) {
                    this.elements.inchargeByBackendUserId.value = defaultUserId;
                }

                // 填充OTA渠道选项
                this.populateOtaChannelOptions();
                
                // 添加子分类提示
                this.addSubCategoryTooltips();
                
                getLogger().log('表单选项填充完成', 'success');
                
            } catch (error) {
                getLogger().logError('populateFormOptions: 填充表单选项时发生错误', {
                    error: error.message,
                    stack: error.stack
                });
                
                // 尝试使用最小的错误恢复
                try {
                    const apiService = getApiService();
                    if (apiService && apiService.staticData) {
                        getLogger().log('尝试从ApiService静态数据恢复', 'warning');
                        const systemData = apiService.staticData;
                        getAppState().setSystemData(systemData);
                    }
                } catch (recoveryError) {
                    getLogger().logError('populateFormOptions: 错误恢复失败', {
                        error: recoveryError.message
                    });
                }
            }
        }

        /**
         * 填充选择框选项
         * @param {HTMLSelectElement} selectElement - 选择框元素
         * @param {Array} options - 选项数组
         * @param {string} valueField - 值字段名
         * @param {string} textField - 文本字段名
         * @param {string} placeholderKey - 占位符的国际化键名（可选）
         */
        populateSelect(selectElement, options, valueField, textField, placeholderKey = null) {
            // 增强的安全检查
            if (!selectElement || !Array.isArray(options)) {
                getLogger().log('populateSelect: 无效的参数', 'warning', {
                    hasElement: !!selectElement,
                    elementType: selectElement ? selectElement.tagName : null,
                    optionsType: typeof options,
                    isArray: Array.isArray(options)
                });
                return;
            }
            
            try {
                // 保存当前选中的值
                const currentValue = selectElement.value;
                
                // 清空现有选项
                selectElement.innerHTML = '';
                
                // 添加占位符选项（如果有）
                if (placeholderKey) {
                    const firstOption = document.createElement('option');
                    firstOption.value = '';

                    // **修复**: 增强i18n管理器获取逻辑，添加重试机制
                    const i18nManager = this.getI18nManagerWithRetry();
                    if (i18nManager) {
                        firstOption.textContent = i18nManager.t(placeholderKey);
                        getLogger().log(`✅ 使用i18n翻译占位符: ${placeholderKey}`, 'info');
                    } else {
                        // 如果i18n管理器不可用，使用默认的中文文本
                        const defaultTexts = {
                            'form.selectServiceType': '请选择服务类型',
                            'form.selectCarType': '请选择车型',
                            'form.selectDrivingRegion': '请选择行驶区域',
                            'form.selectLanguages': '请选择语言',
                            'form.selectOtaChannel': '请选择OTA渠道'
                        };
                        firstOption.textContent = defaultTexts[placeholderKey] || '请选择';

                        // **修复**: 设置data-i18n属性以便后续更新
                        firstOption.setAttribute('data-i18n', placeholderKey);
                        getLogger().log(`⚠️ i18n管理器不可用，使用默认文本: ${placeholderKey}`, 'warn');
                    }
                    firstOption.disabled = true;
                    firstOption.selected = true;
                    selectElement.appendChild(firstOption);
                } else {
                    // 安全的保留原有选项检查
                    try {
                        const existingFirstOption = selectElement.querySelector('option[value=""]');
                        if (existingFirstOption) {
                            selectElement.appendChild(existingFirstOption);
                        }
                    } catch (queryError) {
                        getLogger().logError('populateSelect: querySelector失败', {
                            error: queryError.message,
                            elementId: selectElement.id,
                            elementTagName: selectElement.tagName
                        });
                    }
                }
            } catch (error) {
                getLogger().logError('populateSelect: 操作失败', {
                    error: error.message,
                    elementId: selectElement ? selectElement.id : 'unknown',
                    stack: error.stack
                });
            }
            
            // 安全地添加新选项
            try {
                options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option[valueField];
                    optionElement.textContent = option[textField];
                    selectElement.appendChild(optionElement);
                });
                
                // 恢复之前的选中值
                if (currentValue) {
                    selectElement.value = currentValue;
                }
                
                // **修复**: 监听语言变化事件，自动更新占位符文本
                if (placeholderKey) {
                    const updatePlaceholder = () => {
                        const i18nManagerDeferred = this.getI18nManagerWithRetry();
                        if (i18nManagerDeferred) {
                            try {
                                const firstOption = selectElement.querySelector('option[value=""]');
                                if (firstOption && firstOption.hasAttribute('data-i18n')) {
                                    firstOption.textContent = i18nManagerDeferred.t(placeholderKey);
                                    getLogger().log(`✅ 更新占位符文本: ${placeholderKey}`, 'info');
                                }
                            } catch (i18nError) {
                                getLogger().logError('populateSelect: i18n更新失败', {
                                    error: i18nError.message,
                                    elementId: selectElement.id
                                });
                            }
                        }
                    };

                    // 立即尝试更新
                    setTimeout(updatePlaceholder, 100);

                    // 监听语言变化事件
                    document.addEventListener('languageChanged', updatePlaceholder);
                }
                
            } catch (error) {
                getLogger().logError('populateSelect: 添加选项失败', {
                    error: error.message,
                    elementId: selectElement ? selectElement.id : 'unknown',
                    optionCount: options.length
                });
            }
        }

        /**
         * 从解析数据填充表单
         * @param {object} data - 解析后的数据
         */
        fillFormFromData(data) {
            // 字段映射表：AI返回字段名 → HTML元素ID
            const fieldMapping = {
                'customer_name': 'customerName',
                'customer_contact': 'customerContact', 
                'customer_email': 'customerEmail',
                'pickup_location': 'pickup',
                'dropoff_location': 'dropoff',
                'pickup_date': 'pickupDate',
                'pickup_time': 'pickupTime',
                'passenger_number': 'passengerCount',
                'luggage_count': 'luggageCount',
                'flight_number': 'flightInfo',
                'ota_price': 'otaPrice',
                'currency': 'currency',
                'ota_reference_number': 'otaReferenceNumber',
                'extra_requirement': 'extraRequirement',
                'remark': 'remark',
                'sub_category_id': 'subCategoryId',
                'car_type_id': 'carTypeId',
                'incharge_by_backend_user_id': 'inchargeByBackendUserId',
                'driving_region_id': 'drivingRegionId',
                'languages_id_array': 'languagesIdArray',
                'arrival_time': 'time',
                'departure_time': 'time',
                'time': 'time'
            };

            getLogger().log('开始填充表单数据', 'info', { dataKeys: Object.keys(data) });

            // 遍历数据并填充表单
            for (const snakeCaseKey in data) {
                if (Object.prototype.hasOwnProperty.call(data, snakeCaseKey)) {
                    // 优先使用映射表，如果没有则使用自动转换
                    const elementKey = fieldMapping[snakeCaseKey] || this.snakeToCamel(snakeCaseKey);
                    const element = this.elements[elementKey];

                    // 记录字段映射信息
                    if (fieldMapping[snakeCaseKey]) {
                        getLogger().log(`字段映射: ${snakeCaseKey} → ${elementKey}`, 'info', {
                            value: data[snakeCaseKey],
                            hasElement: !!element
                        });
                    }

                    if (element) {
                        // 处理复选框
                        if (element.type === 'checkbox') {
                            element.checked = Boolean(data[snakeCaseKey]);
                        } else if (element.tagName === 'SELECT') {
                            // 处理下拉框
                            if (elementKey === 'languagesIdArray' && element.multiple) {
                                this.fillLanguageMultiSelect(element, data[snakeCaseKey], snakeCaseKey, data);
                            } else {
                                // 普通下拉框
                                if (data[snakeCaseKey] !== undefined) {
                                    if (!this.safeSetElementValue(element, data[snakeCaseKey], elementKey)) {
                                        // 如果设置失败，尝试应用默认值
                                        const defaultValue = this.getDefaultValueForField(elementKey, data);
                                        if (defaultValue) {
                                            this.safeSetElementValue(element, defaultValue, elementKey);
                                            getLogger().log(`已应用默认值: ${elementKey} = ${defaultValue}`, 'info');
                                        }
                                    }
                                }
                            }
                        } else {
                            // 处理普通输入框
                            if (data[snakeCaseKey] !== undefined) {
                                // 特殊处理日期时间格式
                                if (elementKey === 'date' && data[snakeCaseKey]) {
                                    // 确保日期格式为YYYY-MM-DD
                                    const dateValue = this.formatDateForInput(data[snakeCaseKey]);
                                    this.safeSetElementValue(element, dateValue, elementKey);
                                    getLogger().log(`日期字段填充: ${snakeCaseKey} → ${elementKey}`, 'info', {
                                        original: data[snakeCaseKey],
                                        formatted: dateValue
                                    });
                                } else if (elementKey === 'time' && data[snakeCaseKey]) {
                                    // 时间字段处理 - 按优先级填充
                                    const timeValue = this.formatTimeForInput(data[snakeCaseKey]);
                                    
                                    if (snakeCaseKey === 'pickup_time') {
                                        // 最高优先级：接送时间
                                        this.safeSetElementValue(element, timeValue, elementKey);
                                        getLogger().log(`时间字段填充(最高优先级): ${snakeCaseKey} → ${elementKey}`, 'info', {
                                            original: data[snakeCaseKey],
                                            formatted: timeValue
                                        });
                                    } else if ((snakeCaseKey === 'arrival_time' || snakeCaseKey === 'departure_time')) {
                                        // 中等优先级：到达/出发时间（仅在没有pickup_time时使用）
                                        if (!data.pickup_time && !element.value) {
                                            this.safeSetElementValue(element, timeValue, elementKey);
                                            getLogger().log(`时间字段填充(中等优先级): ${snakeCaseKey} → ${elementKey}`, 'info', {
                                                original: data[snakeCaseKey],
                                                formatted: timeValue
                                            });
                                        }
                                    } else if (snakeCaseKey === 'time') {
                                        // 最低优先级：通用时间字段（仅在没有其他时间字段时使用）
                                        if (!data.pickup_time && !data.arrival_time && !data.departure_time && !element.value) {
                                            this.safeSetElementValue(element, timeValue, elementKey);
                                            getLogger().log(`时间字段填充(最低优先级): ${snakeCaseKey} → ${elementKey}`, 'info', {
                                                original: data[snakeCaseKey],
                                                formatted: timeValue
                                            });
                                        }
                                    }
                                } else {
                                    // 普通字段
                                    this.safeSetElementValue(element, data[snakeCaseKey], elementKey);
                                }
                            }
                        }
                    } else {
                        // 特殊处理：OTA渠道字段
                        if (snakeCaseKey === 'ota' && data[snakeCaseKey]) {
                            getLogger().log(`特殊处理OTA渠道字段: ${snakeCaseKey}`, 'info', { value: data[snakeCaseKey] });
                            this.fillOtaChannelField(data[snakeCaseKey]);
                        }
                    }
                }
            }

            // 应用智能默认值
            this.applySmartDefaults(data);
            
            getLogger().log('表单数据填充完成', 'success');
        }

        /**
         * 收集表单数据
         * @returns {object} 表单数据
         */
        collectFormData() {
            const data = {};
            
            // 基本字段映射
            const fieldMapping = {
                'customerName': 'customer_name',
                'customerContact': 'customer_contact',
                'customerEmail': 'customer_email',
                'pickup': 'pickup_location',
                'dropoff': 'dropoff_location',
                'pickupDate': 'pickup_date',
                'pickupTime': 'pickup_time',
                'passengerCount': 'passenger_number',
                'luggageCount': 'luggage_count',
                'flightInfo': 'flight_number',
                'otaPrice': 'ota_price',
                'driverFee': 'driver_fee',
                'currency': 'currency',
                'otaReferenceNumber': 'ota_reference_number',
                'extraRequirement': 'extra_requirement',
                'remark': 'remark',
                'subCategoryId': 'sub_category_id',
                'carTypeId': 'car_type_id',
                'inchargeByBackendUserId': 'incharge_by_backend_user_id',
                'drivingRegionId': 'driving_region_id'
            };

            // 收集基本字段
            for (const field in fieldMapping) {
                const element = this.elements[field];
                if (element && element.value !== null && element.value.trim() !== '') {
                    const apiField = fieldMapping[field]; // 使用映射表中定义的字段名
                    data[apiField] = element.value.trim();

                    // 记录字段收集过程用于调试
                    getLogger().log('表单字段收集', 'debug', {
                        formField: field,
                        apiField: apiField,
                        value: element.value.trim()
                    });
                }
            }

            // 处理语言多选字段 - 使用统一语言管理器转换
            const selectedLanguages = this.getSelectedLanguages();
            if (selectedLanguages.length > 0) {
                try {
                    const languageManager = getLanguageManager();
                    const languagesObject = languageManager.transformForAPISync(selectedLanguages);
                    data.languages_id_array = languagesObject;
                } catch (error) {
                    getLogger().logError('语言数据转换失败', error);
                    // 使用fallback转换
                    const languagesObject = {};
                    selectedLanguages.forEach((langId, index) => {
                        languagesObject[index.toString()] = langId.toString();
                    });
                    data.languages_id_array = languagesObject;
                }
            }

            // 处理OTA渠道字段
            let otaChannelValue = '';
            if (this.elements.otaChannelCustom && this.elements.otaChannelCustom.value.trim() !== '') {
                otaChannelValue = this.elements.otaChannelCustom.value.trim();
            } else if (this.elements.otaChannel && this.elements.otaChannel.value.trim() !== '') {
                otaChannelValue = this.elements.otaChannel.value.trim();
            }
            if (otaChannelValue) {
                data.ota = otaChannelValue; // 修正：使用正确的API字段名 ota
            }

            // 确保必填字段有默认值
            if (!data.incharge_by_backend_user_id) {
                const defaultBackendUserId = getApiService().getDefaultBackendUserId();
                if (defaultBackendUserId) {
                    data.incharge_by_backend_user_id = defaultBackendUserId;
                    // 同时更新DOM元素
                    if (this.elements.inchargeByBackendUserId) {
                        this.elements.inchargeByBackendUserId.value = defaultBackendUserId;
                    }
                    getLogger().log(`collectFormData: 已设置默认负责人ID: ${defaultBackendUserId}`, 'info');
                } else {
                    getLogger().log('collectFormData: 无法获取默认负责人ID', 'error');
                    // 使用紧急默认值
                    data.incharge_by_backend_user_id = 1;
                    getLogger().log('collectFormData: 使用紧急默认负责人ID: 1', 'warning');
                }
            }

            if (!data.ota_reference_number && this.elements.otaReferenceNumber) {
                const timestamp = Date.now().toString().slice(-6);
                this.elements.otaReferenceNumber.value = `GMH-${timestamp}`;
                data.ota_reference_number = `GMH-${timestamp}`;
            }

            return data;
        }

        /**
         * 将驼峰命名转换为蛇形命名
         * @param {string} str - 驼峰命名字符串
         * @returns {string} 蛇形命名字符串
         */
        camelToSnake(str) {
            return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        }

        /**
         * 将蛇形命名转换为驼峰命名
         * @param {string} str - 蛇形命名字符串
         * @returns {string} 驼峰命名字符串
         */
        snakeToCamel(str) {
            return str.replace(/([-_][a-z])/g, (group) => group.toUpperCase().replace('-', '').replace('_', ''));
        }

        /**
         * 显示验证错误
         * @param {object} errors - 错误对象
         */
        showValidationErrors(errors) {
            let errorMessages = [];
            Object.entries(errors).forEach(([field, messages]) => {
                if (Array.isArray(messages)) {
                    errorMessages = errorMessages.concat(messages);
                } else {
                    errorMessages.push(messages);
                }
            });
            // 这里需要调用UI管理器的showAlert方法
            if (window.OTA.uiManager && window.OTA.uiManager.showAlert) {
                window.OTA.uiManager.showAlert(`验证失败: ${errorMessages.join(', ')}`, 'error');
            }
        }

        /**
         * 显示字段错误
         * @param {string} fieldName - 字段名
         * @param {string} message - 错误消息
         */
        showFieldError(fieldName, message) {
            const element = this.elements[fieldName];
            if (!element) return;

            element.classList.add('error');
            
            // 安全地移除现有错误提示
            try {
                const existingError = element.parentNode ? element.parentNode.querySelector('.field-error') : null;
                if (existingError) {
                    existingError.remove();
                }
            } catch (error) {
                getLogger().logError('showFieldError: 移除现有错误提示失败', {
                    error: error.message,
                    fieldName,
                    elementId: element.id
                });
            }

            // 添加新的错误提示
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = message;
            element.parentNode.appendChild(errorDiv);
        }

        /**
         * 清除字段错误
         * @param {string} fieldName - 字段名
         */
        clearFieldError(fieldName) {
            const element = this.elements[fieldName];
            if (!element) return;

            element.classList.remove('error');
            
            // 安全地移除错误提示
            try {
                const errorDiv = element.parentNode ? element.parentNode.querySelector('.field-error') : null;
                if (errorDiv) {
                    errorDiv.remove();
                }
            } catch (error) {
                getLogger().logError('clearFieldError: 移除错误提示失败', {
                    error: error.message,
                    fieldName,
                    elementId: element.id
                });
            }
        }

        /**
         * 设置实时输入验证
         */
        setupRealtimeValidation() {
            // 邮箱验证
            if (this.elements.customerEmail) {
                this.elements.customerEmail.addEventListener('blur', () => {
                    const email = this.elements.customerEmail.value;
                    if (email && !getApiService().isValidEmail(email)) {
                        this.showFieldError('customerEmail', '邮箱格式不正确');
                    } else {
                        this.clearFieldError('customerEmail');
                    }
                });
            }

            // 电话验证
            if (this.elements.customerContact) {
                this.elements.customerContact.addEventListener('blur', () => {
                    const phone = this.elements.customerContact.value;
                    if (phone && !getApiService().isValidPhone(phone)) {
                        this.showFieldError('customerContact', '电话格式可能不正确');
                    } else {
                        this.clearFieldError('customerContact');
                    }
                });
            }

            // 日期验证
            if (this.elements.date) {
                this.elements.date.addEventListener('change', () => {
                    const date = this.elements.date.value;
                    if (date) {
                        const selectedDate = new Date(date);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0);

                        if (selectedDate < today) {
                            this.showFieldError('date', '不能选择过去的日期');
                        } else {
                            this.clearFieldError('date');
                        }
                    }
                });
            }
        }

        /**
         * 初始化价格转换监听器
         */
        initializePriceConversionListeners() {
            const priceInput = this.elements.otaPrice;
            const currencySelect = this.elements.currency;

            if (!priceInput || !currencySelect) {
                return;
            }

            // 监听价格和货币变化
            const updateConversion = () => {
                // 通过UIManager获取PriceManager实例
                const uiManager = window.OTA?.uiManager || window.uiManager;
                const priceManager = uiManager?.getManager('price');
                if (priceManager && priceManager.updatePriceConversion) {
                    priceManager.updatePriceConversion();
                }
            };

            priceInput.addEventListener('input', updateConversion);
            currencySelect.addEventListener('change', updateConversion);
        }

        /**
         * 填充OTA渠道选项和默认值
         * 根据当前登录用户自动设置OTA渠道
         */
        populateOtaChannelOptions() {
            const appState = getAppState();
            const user = appState.get('auth.user');
            let otaConfig = null;

            // 优先使用用户ID匹配，然后使用邮箱匹配
            if (user) {
                if (user.id) {
                    otaConfig = window.OTA.otaChannelMapping.getConfig(user.id);
                    getLogger().log('通过用户ID匹配OTA配置', 'info', { userId: user.id, hasConfig: !!otaConfig });
                }
                if (!otaConfig && user.email) {
                    otaConfig = window.OTA.otaChannelMapping.getConfig(user.email);
                    getLogger().log('通过邮箱匹配OTA配置', 'info', { email: user.email, hasConfig: !!otaConfig });
                }
            }

            // 记录匹配结果
            if (otaConfig) {
                getLogger().log('找到用户专属OTA配置', 'success', {
                    userId: user.id,
                    email: user.email,
                    defaultChannel: otaConfig.default
                });
            } else {
                getLogger().log('未找到用户专属OTA配置，使用通用配置', 'info', {
                    userId: user ? user.id : null,
                    email: user ? user.email : null
                });
            }

            // 填充OTA渠道下拉框
            if (this.elements.otaChannel) {
                this.elements.otaChannel.innerHTML = '';

                // **修复**: 如果用户有专属配置，不添加占位符选项
                const hasUserConfig = otaConfig && otaConfig.default;
                
                // 只有在没有用户专属配置时才添加占位符
                if (!hasUserConfig) {
                    const placeholderOption = document.createElement('option');
                    placeholderOption.value = '';
                    // **修复**: 使用增强的i18n管理器获取逻辑
                    const i18nManager = this.getI18nManagerWithRetry();
                    if (i18nManager) {
                        placeholderOption.textContent = i18nManager.t('form.selectOtaChannel');
                    } else {
                        placeholderOption.textContent = '请选择OTA渠道';
                        // 设置data-i18n属性以便后续更新
                        placeholderOption.setAttribute('data-i18n', 'form.selectOtaChannel');
                    }
                    placeholderOption.disabled = true;
                    placeholderOption.selected = true;
                    this.elements.otaChannel.appendChild(placeholderOption);
                }

                const options = (otaConfig && otaConfig.options) || (window.OTA.otaChannelMapping && window.OTA.otaChannelMapping.commonChannels) || [];
                
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt.value;
                    option.textContent = opt.text;
                    this.elements.otaChannel.appendChild(option);
                });

                // **修复**: 如果有用户专属配置，直接设置默认值并选中
                if (hasUserConfig) {
                    this.elements.otaChannel.value = otaConfig.default;
                    getLogger().log(`已自动选择OTA渠道: ${otaConfig.default}`, 'success');
                }
            }
        }

        /**
         * 添加子分类选择提示
         */
        addSubCategoryTooltips() {
            if (this.elements.subCategoryId) {
                try {
                    const label = this.elements.subCategoryId.parentElement ? 
                                 this.elements.subCategoryId.parentElement.querySelector('label') : null;
                    if (label) {
                        label.title = '仅支持：接机(Pickup)、送机(Dropoff)、包车(Charter)三种服务类型';
                        label.style.cursor = 'help';
                    }
                } catch (error) {
                    getLogger().logError('addSubCategoryTooltips: 添加提示失败', {
                        error: error.message,
                        elementId: this.elements.subCategoryId.id
                    });
                }
            }
        }

        /**
         * 应用智能默认值
         * @param {object} data - 订单数据
         */
        applySmartDefaults(data) {
            // 智能默认：子分类
            if (!data.sub_category_id && this.elements.subCategoryId) {
                // 本地默认：接机服务
                this.safeSetElementValue(this.elements.subCategoryId, 2, 'subCategoryId');
                getLogger().log('已应用默认子分类: 接机服务', 'info');
            }

            // 智能默认：车型
            if (!data.car_type_id && this.elements.carTypeId) {
                const recommendedCarType = getApiService().recommendCarType(data.passenger_number);
                this.safeSetElementValue(this.elements.carTypeId, recommendedCarType, 'carTypeId');
                getLogger().log(`已应用推荐车型: ${recommendedCarType}`, 'info');
            }

            // 智能默认：负责人
            if (!data.incharge_by_backend_user_id && this.elements.inchargeByBackendUserId) {
                const defaultBackendUserId = getApiService().getDefaultBackendUserId();
                this.safeSetElementValue(this.elements.inchargeByBackendUserId, defaultBackendUserId, 'inchargeByBackendUserId');
                getLogger().log(`已应用默认负责人: ${defaultBackendUserId}`, 'info');
            }

            // 智能默认：参考号
            if (!data.ota_reference_number && this.elements.otaReferenceNumber) {
                const timestamp = Date.now().toString().slice(-6);
                this.safeSetElementValue(this.elements.otaReferenceNumber, `GMH-${timestamp}`, 'otaReferenceNumber');
                getLogger().log(`已生成参考号: GMH-${timestamp}`, 'info');
            }
        }

        /**
         * 安全设置元素值
         * @param {HTMLElement} element - 目标元素
         * @param {*} value - 要设置的值
         * @param {string} fieldName - 字段名（用于日志）
         * @returns {boolean} 是否设置成功
         */
        safeSetElementValue(element, value, fieldName) {
            try {
                if (!element) {
                    getLogger().log(`元素不存在: ${fieldName}`, 'warning');
                    return false;
                }

                if (element.tagName === 'SELECT') {
                    // 检查选项是否存在
                    const optionExists = Array.from(element.options).some(opt => opt.value == value);
                    if (optionExists) {
                        element.value = value;
                        getLogger().log(`下拉框设置成功: ${fieldName} = ${value}`, 'info');
                        return true;
                    } else {
                        getLogger().log(`下拉框选项不存在: ${fieldName} = ${value}`, 'warning', {
                            availableOptions: Array.from(element.options).map(opt => opt.value)
                        });
                        return false;
                    }
                } else {
                    element.value = value;
                    getLogger().log(`字段设置成功: ${fieldName} = ${value}`, 'info');
                    return true;
                }
            } catch (error) {
                getLogger().log(`设置字段值失败: ${fieldName}`, 'error', { error: error.message, value });
                return false;
            }
        }

        /**
         * 获取字段的默认值（用于下拉框值匹配失败时的降级方案）
         * @param {string} fieldName - 字段名（camelCase）
         * @param {object} data - 订单数据
         * @returns {*} 默认值
         */
        getDefaultValueForField(fieldName, data) {
            switch (fieldName) {
                case 'carTypeId':
                    // 基于乘客人数推荐车型，默认5座
                    return getApiService().recommendCarType(data.passenger_number);
                case 'subCategoryId':
                    // 默认接机服务
                    return 2;
                case 'inchargeByBackendUserId':
                    // 默认负责人
                    return getApiService().getDefaultBackendUserId();
                case 'drivingRegionId':
                    // 默认驾驶区域（如果有的话）
                    return 1;
                case 'languagesIdArray':
                    // 使用统一语言管理器的默认策略
                    try {
                        const languageManager = getLanguageManager();
                        return languageManager.getDefaultSelectionSync('form');
                    } catch (error) {
                        getLogger().logError('获取默认语言失败', error);
                        return [2]; // English fallback
                    }
                default:
                    return null;
            }
        }

        /**
         * 填充OTA渠道字段
         * @param {string} otaChannel - OTA渠道值
         */
        fillOtaChannelField(otaChannel) {
            // 优先尝试在下拉框中查找匹配项
            if (this.elements.otaChannel) {
                const optionExists = Array.from(this.elements.otaChannel.options).some(opt => opt.value === otaChannel);
                if (optionExists) {
                    this.elements.otaChannel.value = otaChannel;
                    // 清空自定义输入框
                    if (this.elements.otaChannelCustom) {
                        this.elements.otaChannelCustom.value = '';
                    }
                    getLogger().log(`OTA渠道设置成功(下拉框): ${otaChannel}`, 'info');
                    return;
                }
            }

            // 如果下拉框中没有匹配项，使用自定义输入框
            if (this.elements.otaChannelCustom) {
                this.elements.otaChannelCustom.value = otaChannel;
                getLogger().log(`OTA渠道设置成功(自定义): ${otaChannel}`, 'info');
            }

            // 清空下拉框选择
            if (this.elements.otaChannel) {
                this.elements.otaChannel.value = '';
            }
        }

        /**
         * 填充语言多选字段
         * @param {HTMLSelectElement} element - 多选下拉框元素
         * @param {Array|Object} languageData - 语言数据
         * @param {string} originalField - 原始字段名
         * @param {object} allData - 完整订单数据
         */
        fillLanguageMultiSelect(element, languageData, originalField, allData) {
            // 清除现有选择
            Array.from(element.options).forEach(option => option.selected = false);

            // 解析语言ID数组
            let languageIds = [];
            if (Array.isArray(languageData)) {
                languageIds = languageData.map(id => parseInt(id)).filter(id => !isNaN(id));
            } else if (typeof languageData === 'object' && languageData !== null) {
                // 处理对象格式：{"0":"2","1":"4"}
                languageIds = Object.values(languageData).map(id => parseInt(id)).filter(id => !isNaN(id));
            } else if (typeof languageData === 'string') {
                // 处理字符串格式
                languageIds = languageData.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            }

            // 如果没有语言数据，尝试智能检测
            if (languageIds.length === 0) {
                getLogger().log('语言数据为空，启用智能检测', 'info', {
                    originalField,
                    languageData,
                    remark: allData.remark,
                    extraRequirement: allData.extra_requirement
                });

                // 基于备注和额外要求智能检测语言
                if (window.OTA && window.OTA.geminiService) {
                    const smartLanguages = window.OTA.geminiService.getLanguagesIdArray(
                        allData.remark || allData.extra_requirement,
                        allData.customer_name
                    );
                    if (smartLanguages && smartLanguages.length > 0) {
                        languageIds = smartLanguages;
                        getLogger().log('智能语言检测成功', 'success', { detectedLanguages: languageIds });
                    }
                }

                // 如果智能检测也失败，使用统一管理器的默认策略
                if (languageIds.length === 0) {
                    try {
                        const languageManager = getLanguageManager();
                        languageIds = languageManager.getDefaultSelectionSync('form');
                        getLogger().log('使用统一管理器默认语言', 'info', { defaultLanguages: languageIds });
                    } catch (error) {
                        languageIds = [2]; // Ultimate fallback
                        getLogger().log('使用终极备用语言: 英语', 'info');
                    }
                }
            }

            // 应用语言选择
            let appliedCount = 0;
            languageIds.forEach(langId => {
                const option = Array.from(element.options).find(opt => parseInt(opt.value) === langId);
                if (option) {
                    option.selected = true;
                    appliedCount++;
                }
            });

            getLogger().log(`语言多选设置完成: ${originalField}`, 'info', {
                requestedLanguages: languageIds,
                appliedCount,
                totalOptions: element.options.length
            });

            // 更新多选下拉菜单显示（如果存在）
            if (this.languagesDropdown) {
                this.languagesDropdown.setSelectedValues(languageIds.map(id => id.toString()));
            }
        }

        /**
         * 获取选中的语言ID数组
         * @returns {Array} 语言ID数组
         */
        getSelectedLanguages() {
            // 获取所有语言复选框
            const checkboxes = document.querySelectorAll('input[name="languagesIdArray"]:checked');
            return Array.from(checkboxes)
                .map(checkbox => checkbox.value)
                .filter(value => value && value.trim() !== '');
        }

        /**
         * 设置语言选择状态
         * @param {Array} languageIds - 语言ID数组 [2, 4] 等
         */
        setLanguageSelection(languageIds) {
            try {
                if (!Array.isArray(languageIds) || languageIds.length === 0) {
                    getLogger().log('setLanguageSelection: 无效的语言ID数组', 'warning');
                    return;
                }

                // 方法1: 尝试使用多选下拉组件
                if (this.languagesDropdown && typeof this.languagesDropdown.setSelectedValues === 'function') {
                    const stringIds = languageIds.map(id => id.toString());
                    this.languagesDropdown.setSelectedValues(stringIds);
                    getLogger().log('通过多选下拉组件设置语言选择', 'info', { languageIds, stringIds });
                    return;
                }

                // 方法2: 直接操作复选框
                // 首先清除所有选中状态
                const allCheckboxes = document.querySelectorAll('input[name="languagesIdArray"]');
                allCheckboxes.forEach(checkbox => {
                    checkbox.checked = false;
                });

                // 设置指定语言的选中状态
                languageIds.forEach(id => {
                    const checkbox = document.querySelector(`input[name="languagesIdArray"][value="${id}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });

                // 方法3: 触发相关的UI更新事件
                const languageField = this.elements.languagesIdArray;
                if (languageField) {
                    // 触发change事件以更新UI显示
                    const changeEvent = new Event('change', { bubbles: true });
                    languageField.dispatchEvent(changeEvent);
                }

                getLogger().log('已设置语言选择', 'info', { 
                    selectedLanguageIds: languageIds,
                    method: 'checkbox manipulation'
                });

            } catch (error) {
                getLogger().logError('设置语言选择失败', error);
            }
        }

        /**
         * **新增**: 获取i18n管理器，带重试机制
         * @returns {Object|null} i18n管理器实例
         */
        getI18nManagerWithRetry() {
            // 尝试多种方式获取i18n管理器
            const attempts = [
                () => window.getI18nManager && window.getI18nManager(),
                () => window.OTA && window.OTA.i18nManager,
                () => window.i18nManager
            ];

            for (const attempt of attempts) {
                try {
                    const manager = attempt();
                    if (manager && typeof manager.t === 'function') {
                        return manager;
                    }
                } catch (error) {
                    // 继续尝试下一种方式
                }
            }

            return null;
        }

        /**
         * 格式化日期为HTML input[type="date"]所需的格式 (YYYY-MM-DD)
         * @param {string} dateString - 原始日期字符串
         * @returns {string} 格式化后的日期字符串
         */
        formatDateForInput(dateString) {
            try {
                // 尝试解析各种日期格式
                const date = new Date(dateString);
                if (isNaN(date.getTime())) {
                    getLogger().log('日期格式无效，使用今天日期', 'warning', { input: dateString });
                    return new Date().toISOString().split('T')[0];
                }
                return date.toISOString().split('T')[0];
            } catch (error) {
                getLogger().log('日期解析失败，使用今天日期', 'error', { input: dateString, error: error.message });
                return new Date().toISOString().split('T')[0];
            }
        }

        /**
         * 格式化时间为HTML input[type="time"]所需的格式 (HH:MM)
         * @param {string} timeString - 原始时间字符串
         * @returns {string} 格式化后的时间字符串
         */
        formatTimeForInput(timeString) {
            try {
                // 处理各种时间格式
                if (timeString.includes(':')) {
                    // 已经是HH:MM或HH:MM:SS格式
                    const timeParts = timeString.split(':');
                    return `${timeParts[0].padStart(2, '0')}:${timeParts[1].padStart(2, '0')}`;
                } else {
                    // 尝试解析为完整日期时间
                    const date = new Date(timeString);
                    if (!isNaN(date.getTime())) {
                        return date.toTimeString().slice(0, 5); // HH:MM
                    }
                }

                getLogger().log('时间格式无效，使用当前时间', 'warning', { input: timeString });
                return new Date().toTimeString().slice(0, 5);
            } catch (error) {
                getLogger().log('时间解析失败，使用当前时间', 'error', { input: timeString, error: error.message });
                return new Date().toTimeString().slice(0, 5);
            }
        }

        /**
         * 重置表单
         */
        resetForm() {
            // 重置表单
            if (this.elements.orderForm) {
                this.elements.orderForm.reset();
            }
            if (this.elements.orderInput) {
                this.elements.orderInput.value = '';
            }

            // 清除所有字段错误
            Object.keys(this.elements).forEach(fieldName => {
                this.clearFieldError(fieldName);
            });

            // 重新应用默认值
            this.applySmartDefaults({});

            getLogger().log('表单已重置', 'success');
        }

        /**
         * 初始化自适应高度文本框
         * 为额外要求文本框添加自动调整高度功能
         */
        initAutoResizeTextarea() {
            const textarea = document.getElementById('extraRequirement');
            if (!textarea) {
                return;
            }

            // 自动调整高度的函数
            const autoResize = () => {
                // 重置高度以获取正确的scrollHeight
                textarea.style.height = 'auto';

                // 响应式高度计算：移动端使用较小的尺寸
                const isMobile = window.innerWidth <= 768;
                const minHeight = isMobile ? 50 : 60; // 移动端50px，桌面端60px
                const maxHeight = isMobile ? 150 : 200; // 移动端150px，桌面端200px

                // 计算新高度
                const newHeight = Math.min(
                    Math.max(textarea.scrollHeight, minHeight),
                    maxHeight
                );

                // 设置新高度
                textarea.style.height = newHeight + 'px';
            };

            // 绑定事件监听器
            textarea.addEventListener('input', autoResize);
            textarea.addEventListener('paste', () => {
                // 粘贴后稍微延迟执行，确保内容已插入
                setTimeout(autoResize, 10);
            });

            // 窗口大小改变时重新调整高度（响应式支持）
            window.addEventListener('resize', () => {
                setTimeout(autoResize, 100);
            });

            // 初始调整
            autoResize();

            getLogger().log('自适应高度文本框初始化完成', 'success');
        }
    }

    // 导出到全局命名空间
    window.OTA.managers.FormManager = FormManager;

})();
