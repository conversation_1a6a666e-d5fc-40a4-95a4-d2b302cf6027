# 🧪 Tests 文件夹

此文件夹包含OTA订单处理系统的所有测试文件和测试套件。

## 📁 文件夹结构

### 单元测试 (unit/)
- `managers/` - 管理器类的单元测试
- `services/` - 服务类的单元测试
- `utils/` - 工具函数的单元测试

### 集成测试 (integration/)
- 模块间集成测试
- API集成测试
- 数据流测试

### 端到端测试 (e2e/)
- 完整用户流程测试
- 浏览器自动化测试
- 功能验收测试

## 📋 测试文件

### 核心测试套件
- `test-suites.js` - 主要测试套件
- `performance-test-suite.js` - 性能测试套件

### HTML测试页面
- `test-refactor-integration.html` - 重构集成测试页面
- `test-comprehensive-fixes.html` - 综合修复测试页面
- `test-dropdown-fixes.html` - 下拉框修复测试页面
- `test-fixes-verification.html` - 修复验证测试页面
- `test-language-unification-fixes.html` - 语言统一修复测试页面
- `test-language-unification.html` - 语言统一测试页面
- `test-multi-select-fixes.html` - 多选修复测试页面

### 诊断和完成页面
- `diagnose-service-panel.html` - 服务面板诊断页面
- `language-unification-completion.html` - 语言统一完成页面

## 🚀 运行测试

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e
```

### HTML测试页面使用方法
1. 在浏览器中打开对应的HTML测试页面
2. 点击测试按钮执行相应的测试
3. 查看测试结果和控制台输出

## 📝 测试规范

1. **命名规范**：测试文件以 `test-` 开头
2. **文件组织**：按功能模块组织测试文件
3. **测试覆盖**：确保关键功能有充分的测试覆盖
4. **文档说明**：每个测试文件应有清晰的说明
5. **HTML测试**：交互式测试页面应包含清晰的操作说明

## 🔍 测试类型说明

### 功能测试
- **重构集成测试**：验证模块化重构后的系统完整性
- **修复验证测试**：确认Bug修复的有效性
- **语言统一测试**：验证多语言支持功能

### 诊断测试
- **服务面板诊断**：检查服务面板功能状态
- **下拉框修复测试**：验证UI组件修复效果
- **多选功能测试**：确认多选组件正常工作

---
*最后更新：2025年1月*
