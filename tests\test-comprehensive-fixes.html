<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多选组件根源性修复验证测试</title>
    
    <!-- CSS基础样式 -->
    <link rel="stylesheet" href="css/base/variables.css">
    <link rel="stylesheet" href="css/components/forms.css">
    
    <style>
        body {
            font-family: var(--font-family);
            background: var(--bg-primary);
            color: var(--text-primary);
            padding: var(--spacing-8);
            margin: 0;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: var(--bg-card);
            padding: var(--spacing-8);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-card);
        }
        
        .test-section {
            margin-bottom: var(--spacing-8);
            padding: var(--spacing-6);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
        }
        
        .test-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--color-primary);
            margin-bottom: var(--spacing-4);
        }
        
        .test-description {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-6);
            line-height: var(--line-height-relaxed);
        }
        
        .status-panel {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--spacing-2);
            font-size: var(--font-size-sm);
        }
        
        .status-value {
            font-weight: 600;
        }
        
        .status-value.success { color: var(--color-success); }
        .status-value.warning { color: var(--color-warning); }
        .status-value.error { color: var(--color-error); }
        
        .controls {
            display: flex;
            gap: var(--spacing-3);
            margin-bottom: var(--spacing-6);
            flex-wrap: wrap;
        }
        
        .btn {
            padding: var(--spacing-2) var(--spacing-4);
            background: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: var(--font-size-sm);
            transition: all var(--transition-fast);
        }
        
        .btn:hover {
            background: var(--color-primary-hover);
        }
        
        .btn.secondary {
            background: var(--color-secondary);
        }
        
        .btn.secondary:hover {
            background: var(--color-secondary-hover);
        }
        
        .test-log {
            background: #000;
            color: #00ff00;
            padding: var(--spacing-4);
            border-radius: var(--radius-md);
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .architecture-test {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-4);
        }
        
        .z-index-test {
            position: relative;
        }
        
        .test-layer {
            position: absolute;
            padding: var(--spacing-2);
            border: 1px solid;
            border-radius: var(--radius-sm);
            color: white;
            font-size: var(--font-size-xs);
        }
        
        .layer-dropdown {
            background: var(--color-primary);
            z-index: var(--z-dropdown);
            top: 0;
            left: 0;
        }
        
        .layer-modal {
            background: var(--color-warning);
            z-index: var(--z-modal);
            top: 20px;
            left: 20px;
        }
        
        .layer-overlay {
            background: var(--color-error);
            z-index: var(--z-overlay);
            top: 40px;
            left: 40px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>多选组件根源性修复验证</h1>
        <p>全面测试架构级解决方案的效果</p>
        
        <!-- 系统状态面板 -->
        <div class="test-section">
            <div class="test-title">系统架构状态</div>
            <div class="status-panel" id="systemStatus">
                <div class="status-item">
                    <span>依赖容器状态:</span>
                    <span class="status-value" id="containerStatus">检查中...</span>
                </div>
                <div class="status-item">
                    <span>事件协调器状态:</span>
                    <span class="status-value" id="eventCoordinatorStatus">检查中...</span>
                </div>
                <div class="status-item">
                    <span>生命周期管理器状态:</span>
                    <span class="status-value" id="lifecycleStatus">检查中...</span>
                </div>
                <div class="status-item">
                    <span>已注册组件数量:</span>
                    <span class="status-value" id="componentCount">0</span>
                </div>
                <div class="status-item">
                    <span>活跃组件数量:</span>
                    <span class="status-value" id="activeComponentCount">0</span>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="checkSystemHealth()">检查系统健康度</button>
                <button class="btn secondary" onclick="performArchitectureTest()">架构完整性测试</button>
                <button class="btn secondary" onclick="clearLog()">清空日志</button>
            </div>
            
            <div class="test-log" id="systemLog"></div>
        </div>
        
        <!-- 多选组件功能测试 -->
        <div class="test-section">
            <div class="test-title">多选组件功能测试</div>
            <div class="test-description">
                测试新架构下多选组件的各项功能
            </div>
            
            <div class="architecture-test">
                <div>
                    <h4>依赖注入方式创建的组件</h4>
                    <div class="form-group">
                        <label>语言选择 (依赖注入)</label>
                        <div class="multi-select-dropdown" id="diMultiSelect">
                            <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                                <span class="multi-select-text placeholder">选择语言</span>
                                <span class="multi-select-arrow">▼</span>
                            </div>
                            <div class="multi-select-options">
                                <!-- 动态加载选项 -->
                            </div>
                            <select multiple style="display: none;">
                                <option value="1">English</option>
                                <option value="2">中文</option>
                                <option value="3">Malay</option>
                                <option value="4">Tamil</option>
                                <option value="5">Thai</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4>传统方式创建的组件</h4>
                    <div class="form-group">
                        <label>语言选择 (传统方式)</label>
                        <div class="multi-select-dropdown" id="traditionalMultiSelect">
                            <div class="multi-select-trigger" tabindex="0" aria-expanded="false">
                                <span class="multi-select-text placeholder">选择语言</span>
                                <span class="multi-select-arrow">▼</span>
                            </div>
                            <div class="multi-select-options">
                                <!-- 动态加载选项 -->
                            </div>
                            <select multiple style="display: none;">
                                <option value="1">English</option>
                                <option value="2">中文</option>
                                <option value="3">Malay</option>
                                <option value="4">Tamil</option>
                                <option value="5">Japanese</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Z-Index层级测试 -->
        <div class="test-section">
            <div class="test-title">Z-Index层级测试</div>
            <div class="test-description">
                验证新的Z-Index层级系统是否正确工作
            </div>
            
            <div class="z-index-test" style="height: 150px; position: relative;">
                <div class="test-layer layer-dropdown">下拉菜单层 (z-index: var(--z-dropdown))</div>
                <div class="test-layer layer-modal">模态框层 (z-index: var(--z-modal))</div>
                <div class="test-layer layer-overlay">遮罩层 (z-index: var(--z-overlay))</div>
            </div>
        </div>
        
        <!-- 事件管理测试 -->
        <div class="test-section">
            <div class="test-title">事件管理测试</div>
            <div class="test-description">
                测试统一事件协调器的工作效果
            </div>
            
            <div class="controls">
                <button class="btn" onclick="testEventDelegation()">测试事件委托</button>
                <button class="btn secondary" onclick="testGlobalClick()">测试全局点击</button>
                <button class="btn secondary" onclick="testKeyboardNavigation()">测试键盘导航</button>
            </div>
            
            <div id="eventTestResults" style="margin-top: var(--spacing-4);"></div>
        </div>
    </div>
    
    <!-- 加载脚本 - 按正确顺序 -->
    <!-- 核心架构 -->
    <script src="js/core/dependency-container.js"></script>
    <script src="js/core/service-locator.js"></script>
    <script src="js/core/global-event-coordinator.js"></script>
    <script src="js/core/component-lifecycle-manager.js"></script>
    
    <!-- 基础工具 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/language-manager.js"></script>
    
    <!-- UI组件 -->
    <script src="js/multi-select-dropdown.js"></script>
    
    <script>
        let systemLog = '';
        let diComponent = null;
        let traditionalComponent = null;
        
        // 初始化系统
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 开始系统初始化测试...');
            
            // 初始化核心组件
            initializeCore();
            
            // 检查系统状态
            setTimeout(() => {
                checkSystemHealth();
                createTestComponents();
            }, 1000);
        });
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            systemLog += `[${timestamp}] ${message}\n`;
            const logElement = document.getElementById('systemLog');
            if (logElement) {
                logElement.textContent = systemLog;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }
        
        function clearLog() {
            systemLog = '';
            document.getElementById('systemLog').textContent = '';
        }
        
        function initializeCore() {
            try {
                // 初始化事件协调器
                if (window.OTA && window.OTA.globalEventCoordinator) {
                    window.OTA.globalEventCoordinator.init();
                    log('✅ 事件协调器已初始化');
                }
                
                // 初始化生命周期管理器
                if (window.OTA && window.OTA.componentLifecycleManager) {
                    window.OTA.componentLifecycleManager.init();
                    log('✅ 生命周期管理器已初始化');
                }
                
                log('✅ 核心组件初始化完成');
            } catch (error) {
                log(`❌ 核心组件初始化失败: ${error.message}`);
            }
        }
        
        function checkSystemHealth() {
            log('🔍 开始系统健康检查...');
            
            // 检查依赖容器
            const containerStatus = checkDependencyContainer();
            updateStatus('containerStatus', containerStatus.status, containerStatus.text);
            
            // 检查事件协调器
            const eventStatus = checkEventCoordinator();
            updateStatus('eventCoordinatorStatus', eventStatus.status, eventStatus.text);
            
            // 检查生命周期管理器
            const lifecycleStatus = checkLifecycleManager();
            updateStatus('lifecycleStatus', lifecycleStatus.status, lifecycleStatus.text);
            
            // 更新组件计数
            updateComponentCounts();
            
            log('✅ 系统健康检查完成');
        }
        
        function checkDependencyContainer() {
            if (window.OTA && window.OTA.container) {
                const status = window.OTA.container.getStatus();
                log(`📦 依赖容器: ${status.registeredServices} 个服务, ${status.createdInstances} 个实例`);
                return { status: 'success', text: `${status.registeredServices} 个服务` };
            }
            return { status: 'error', text: '未找到' };
        }
        
        function checkEventCoordinator() {
            if (window.OTA && window.OTA.globalEventCoordinator) {
                const components = window.OTA.globalEventCoordinator.getAllComponents();
                log(`🎯 事件协调器: 管理 ${components.length} 个组件`);
                return { status: 'success', text: `管理 ${components.length} 个组件` };
            }
            return { status: 'error', text: '未找到' };
        }
        
        function checkLifecycleManager() {
            if (window.OTA && window.OTA.componentLifecycleManager) {
                const status = window.OTA.componentLifecycleManager.getStatus();
                log(`🔄 生命周期管理器: ${status.totalComponents} 个组件, ${status.activeComponents} 个活跃`);
                return { status: 'success', text: `${status.totalComponents} 个组件` };
            }
            return { status: 'error', text: '未找到' };
        }
        
        function updateStatus(elementId, status, text) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                element.className = `status-value ${status}`;
            }
        }
        
        function updateComponentCounts() {
            let totalComponents = 0;
            let activeComponents = 0;
            
            if (window.OTA && window.OTA.componentLifecycleManager) {
                const status = window.OTA.componentLifecycleManager.getStatus();
                totalComponents = status.totalComponents;
                activeComponents = status.activeComponents;
            }
            
            document.getElementById('componentCount').textContent = totalComponents;
            document.getElementById('activeComponentCount').textContent = activeComponents;
        }
        
        function createTestComponents() {
            log('🛠️ 创建测试组件...');
            
            try {
                // 使用依赖注入方式创建组件
                if (window.OTA && window.OTA.container) {
                    try {
                        const factory = window.OTA.container.get('MultiSelectDropdownFactory');
                        diComponent = factory.create('diMultiSelect', {
                            placeholder: '选择语言 (DI)',
                            maxDisplayItems: 3,
                            closeOnSelect: false,
                            autoClose: false
                        });
                        log('✅ 依赖注入组件创建成功');
                    } catch (e) {
                        log(`⚠️ 依赖注入创建失败，使用降级方案: ${e.message}`);
                        diComponent = new MultiSelectDropdown('diMultiSelect', {
                            placeholder: '选择语言 (DI降级)',
                            maxDisplayItems: 3
                        });
                    }
                }
                
                // 使用传统方式创建组件
                traditionalComponent = new MultiSelectDropdown('traditionalMultiSelect', {
                    placeholder: '选择语言 (传统)',
                    maxDisplayItems: 3,
                    closeOnSelect: false,
                    autoClose: false
                });
                
                log('✅ 传统方式组件创建成功');
                
                setTimeout(() => {
                    checkSystemHealth();
                }, 500);
                
            } catch (error) {
                log(`❌ 组件创建失败: ${error.message}`);
            }
        }
        
        function performArchitectureTest() {
            log('🏗️ 开始架构完整性测试...');
            
            // 测试1: 依赖注入
            testDependencyInjection();
            
            // 测试2: 事件协调
            testEventCoordination();
            
            // 测试3: 生命周期管理
            testLifecycleManagement();
            
            // 测试4: 组件隔离
            testComponentIsolation();
            
            log('✅ 架构完整性测试完成');
        }
        
        function testDependencyInjection() {
            log('  📦 测试依赖注入...');
            
            if (window.OTA && window.OTA.container) {
                const services = window.OTA.container.getRegisteredServices();
                log(`    - 已注册 ${services.length} 个服务: ${services.join(', ')}`);
                
                try {
                    const factory = window.OTA.container.get('MultiSelectDropdownFactory');
                    log('    ✅ MultiSelectDropdown工厂可用');
                } catch (e) {
                    log('    ❌ MultiSelectDropdown工厂不可用');
                }
            }
        }
        
        function testEventCoordination() {
            log('  🎯 测试事件协调...');
            
            if (window.OTA && window.OTA.globalEventCoordinator) {
                const components = window.OTA.globalEventCoordinator.getAllComponents();
                log(`    - 事件协调器管理 ${components.length} 个组件`);
                components.forEach(comp => {
                    log(`      ${comp.id} (${comp.type}) - ${comp.isActive ? '活跃' : '非活跃'}`);
                });
            }
        }
        
        function testLifecycleManagement() {
            log('  🔄 测试生命周期管理...');
            
            if (window.OTA && window.OTA.componentLifecycleManager) {
                const healthCheck = window.OTA.componentLifecycleManager.performHealthCheck();
                log(`    - 健康组件: ${healthCheck.healthyComponents}/${healthCheck.totalComponents}`);
                
                if (healthCheck.issues.length > 0) {
                    healthCheck.issues.forEach(issue => {
                        log(`      ⚠️ ${issue.componentId}: ${issue.message}`);
                    });
                }
            }
        }
        
        function testComponentIsolation() {
            log('  🧱 测试组件隔离...');
            
            // 模拟同时打开多个组件
            if (diComponent && traditionalComponent) {
                diComponent.open();
                traditionalComponent.open();
                
                setTimeout(() => {
                    const diOpen = diComponent.isOpen;
                    const tradOpen = traditionalComponent.isOpen;
                    
                    log(`    - DI组件状态: ${diOpen ? '打开' : '关闭'}`);
                    log(`    - 传统组件状态: ${tradOpen ? '打开' : '关闭'}`);
                    
                    // 关闭组件
                    diComponent.close();
                    traditionalComponent.close();
                }, 500);
            }
        }
        
        function testEventDelegation() {
            log('🎯 测试事件委托机制...');
            
            // 模拟全局点击事件
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            
            document.body.dispatchEvent(event);
            log('  ✅ 全局点击事件已触发');
        }
        
        function testGlobalClick() {
            if (diComponent) {
                diComponent.open();
                log('🖱️ 打开DI组件，3秒后模拟全局点击关闭');
                
                setTimeout(() => {
                    // 模拟点击外部区域
                    const clickEvent = new MouseEvent('click', {
                        bubbles: true,
                        cancelable: true
                    });
                    document.body.dispatchEvent(clickEvent);
                    
                    setTimeout(() => {
                        const isStillOpen = diComponent.isOpen;
                        log(`  ${isStillOpen ? '❌ 组件未关闭' : '✅ 组件已自动关闭'}`);
                    }, 100);
                }, 3000);
            }
        }
        
        function testKeyboardNavigation() {
            if (traditionalComponent) {
                traditionalComponent.open();
                log('⌨️ 打开传统组件，3秒后模拟ESC键关闭');
                
                setTimeout(() => {
                    const escEvent = new KeyboardEvent('keydown', {
                        key: 'Escape',
                        bubbles: true,
                        cancelable: true
                    });
                    document.dispatchEvent(escEvent);
                    
                    setTimeout(() => {
                        const isStillOpen = traditionalComponent.isOpen;
                        log(`  ${isStillOpen ? '❌ 组件未关闭' : '✅ 组件已通过ESC关闭'}`);
                    }, 100);
                }, 3000);
            }
        }
        
        // 定期更新状态
        setInterval(() => {
            updateComponentCounts();
        }, 2000);
    </script>
</body>
</html>