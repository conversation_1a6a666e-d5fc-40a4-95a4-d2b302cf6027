<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM测试修复验证</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 LLM测试修复验证</h1>
    <div id="results"></div>
    
    <!-- 引入必要的脚本 -->
    <script src="../js/logger.js"></script>
    <script src="../js/core/dependency-container.js"></script>
    <script src="../js/app-state.js"></script>
    <script src="../js/gemini-service.js"></script>
    <script src="../js/kimi-service.js"></script>
    
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }
        
        async function runTests() {
            addResult('开始测试修复...', 'info');
            
            try {
                // 测试服务可用性
                const geminiService = window.getGeminiService();
                const kimiService = window.getKimiService();
                
                addResult('✅ Gemini服务加载成功', 'success');
                addResult('✅ Kimi服务加载成功', 'success');
                
                // 测试方法存在性
                if (typeof geminiService.parseOrder === 'function') {
                    addResult('✅ Gemini.parseOrder方法存在', 'success');
                } else {
                    addResult('❌ Gemini.parseOrder方法不存在', 'error');
                }
                
                if (typeof kimiService.parseOrderText === 'function') {
                    addResult('✅ Kimi.parseOrderText方法存在', 'success');
                } else {
                    addResult('❌ Kimi.parseOrderText方法不存在', 'error');
                }
                
                // 测试简单解析
                const testText = "接机服务，客户：张三，电话：+60123456789，2024-12-25 14:30从KLIA2到酒店，2人1行李";
                
                addResult('🔄 测试Gemini解析...', 'info');
                try {
                    const geminiResult = await geminiService.parseOrder(testText);
                    addResult(`✅ Gemini解析成功: ${Array.isArray(geminiResult) ? geminiResult.length : 0}个订单`, 'success');
                } catch (error) {
                    addResult(`❌ Gemini解析失败: ${error.message}`, 'error');
                }
                
                addResult('🔄 测试Kimi解析...', 'info');
                try {
                    const kimiResult = await kimiService.parseOrderText(testText);
                    addResult(`✅ Kimi解析成功: ${kimiResult.success ? kimiResult.orders.length : 0}个订单`, 'success');
                } catch (error) {
                    addResult(`❌ Kimi解析失败: ${error.message}`, 'error');
                }
                
                addResult('🎉 所有测试完成！', 'success');
                
            } catch (error) {
                addResult(`❌ 测试过程出错: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>