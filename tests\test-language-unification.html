<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语言数据统一管理系统验证测试</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px; 
            line-height: 1.6;
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
        }
        .test-header { 
            font-weight: bold; 
            color: #333; 
            margin-bottom: 10px; 
        }
        .test-result { 
            margin: 10px 0; 
            padding: 10px; 
            border-radius: 3px; 
        }
        .success { 
            background-color: #d4edda; 
            border: 1px solid #c3e6cb; 
            color: #155724; 
        }
        .error { 
            background-color: #f8d7da; 
            border: 1px solid #f5c6cb; 
            color: #721c24; 
        }
        .info { 
            background-color: #d1ecf1; 
            border: 1px solid #bee5eb; 
            color: #0c5460; 
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 语言数据统一管理系统验证测试</h1>
    <p>本测试验证统一语言管理器的功能和数据一致性</p>

    <div class="test-section">
        <div class="test-header">🧪 测试控制</div>
        <button class="test-button" onclick="runAllTests()">运行所有测试</button>
        <button class="test-button" onclick="clearResults()">清除结果</button>
        <button class="test-button" onclick="showCacheStatus()">缓存状态</button>
    </div>

    <div class="test-section">
        <div class="test-header">📊 测试结果概览</div>
        <div id="testOverview">等待测试运行...</div>
    </div>

    <div class="test-section">
        <div class="test-header">1️⃣ 语言管理器基础功能测试</div>
        <div id="basicTests">等待测试...</div>
    </div>

    <div class="test-section">
        <div class="test-header">2️⃣ 数据一致性验证测试</div>
        <div id="consistencyTests">等待测试...</div>
    </div>

    <div class="test-section">
        <div class="test-header">3️⃣ API格式转换测试</div>
        <div id="apiFormatTests">等待测试...</div>
    </div>

    <div class="test-section">
        <div class="test-header">4️⃣ 默认值策略测试</div>
        <div id="defaultValueTests">等待测试...</div>
    </div>

    <div class="test-section">
        <div class="test-header">5️⃣ 性能和缓存测试</div>
        <div id="performanceTests">等待测试...</div>
    </div>

    <div class="test-section">
        <div class="test-header">📝 详细日志</div>
        <div id="detailedLog">等待测试...</div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/logger.js"></script>
    <script src="js/app-state.js"></script>
    <script src="js/language-manager.js"></script>

    <script>
        // 测试计数器
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            details: []
        };

        // 日志收集器
        let detailedLogs = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            detailedLogs.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }

        function addTestResult(testName, success, message, data = null) {
            testResults.total++;
            if (success) {
                testResults.passed++;
            } else {
                testResults.failed++;
            }
            
            testResults.details.push({
                name: testName,
                success,
                message,
                data,
                timestamp: new Date().toISOString()
            });
        }

        function updateResults(containerId, content) {
            document.getElementById(containerId).innerHTML = content;
        }

        async function runAllTests() {
            // 重置测试结果
            testResults = { total: 0, passed: 0, failed: 0, details: [] };
            detailedLogs = [];
            
            log('开始运行语言数据统一管理系统验证测试');
            
            try {
                await test1_BasicFunctionality();
                await test2_DataConsistency();
                await test3_ApiFormatConversion();
                await test4_DefaultValueStrategy();
                await test5_PerformanceAndCaching();
                
                updateOverview();
                updateDetailedLog();
                
                log(`测试完成: ${testResults.passed}/${testResults.total} 通过`);
            } catch (error) {
                log(`测试运行失败: ${error.message}`, 'error');
            }
        }

        async function test1_BasicFunctionality() {
            log('开始基础功能测试');
            let results = [];
            
            try {
                // 测试1.1: 语言管理器实例化
                const languageManager = getLanguageManager();
                if (languageManager) {
                    addTestResult('语言管理器实例化', true, '成功获取语言管理器实例');
                    results.push('<div class="test-result success">✅ 语言管理器实例化成功</div>');
                } else {
                    addTestResult('语言管理器实例化', false, '无法获取语言管理器实例');
                    results.push('<div class="test-result error">❌ 语言管理器实例化失败</div>');
                }

                // 测试1.2: 获取语言列表
                try {
                    const languages = await languageManager.getLanguages();
                    if (languages && languages.length > 0) {
                        addTestResult('获取语言列表', true, `成功获取${languages.length}种语言`);
                        results.push(`<div class="test-result success">✅ 成功获取${languages.length}种语言</div>`);
                        results.push(`<pre>${JSON.stringify(languages.slice(0, 3), null, 2)}</pre>`);
                    } else {
                        addTestResult('获取语言列表', false, '语言列表为空');
                        results.push('<div class="test-result error">❌ 语言列表为空</div>');
                    }
                } catch (error) {
                    addTestResult('获取语言列表', false, `获取语言列表失败: ${error.message}`);
                    results.push(`<div class="test-result error">❌ 获取语言列表失败: ${error.message}</div>`);
                }

                // 测试1.3: 语言ID验证
                try {
                    const validation = await languageManager.validateLanguageIds([2, 3, 4, 999]);
                    if (validation.valid === false && validation.invalidIds.includes(999)) {
                        addTestResult('语言ID验证', true, '正确识别了无效的语言ID');
                        results.push('<div class="test-result success">✅ 语言ID验证功能正常</div>');
                        results.push(`<pre>验证结果: ${JSON.stringify(validation, null, 2)}</pre>`);
                    } else {
                        addTestResult('语言ID验证', false, '语言ID验证功能异常');
                        results.push('<div class="test-result error">❌ 语言ID验证功能异常</div>');
                    }
                } catch (error) {
                    addTestResult('语言ID验证', false, `语言ID验证失败: ${error.message}`);
                    results.push(`<div class="test-result error">❌ 语言ID验证失败: ${error.message}</div>`);
                }

            } catch (error) {
                addTestResult('基础功能测试', false, `基础功能测试失败: ${error.message}`);
                results.push(`<div class="test-result error">❌ 基础功能测试失败: ${error.message}</div>`);
            }
            
            updateResults('basicTests', results.join(''));
        }

        async function test2_DataConsistency() {
            log('开始数据一致性验证测试');
            let results = [];
            
            try {
                const languageManager = getLanguageManager();
                
                // 测试2.1: 多次获取数据一致性
                const data1 = await languageManager.getLanguages();
                const data2 = await languageManager.getLanguages();
                
                if (JSON.stringify(data1) === JSON.stringify(data2)) {
                    addTestResult('数据一致性', true, '多次获取的数据完全一致');
                    results.push('<div class="test-result success">✅ 多次获取的数据完全一致</div>');
                } else {
                    addTestResult('数据一致性', false, '多次获取的数据不一致');
                    results.push('<div class="test-result error">❌ 多次获取的数据不一致</div>');
                }

                // 测试2.2: 缓存机制验证
                const startTime = performance.now();
                await languageManager.getLanguages();
                const firstCallTime = performance.now() - startTime;
                
                const startTime2 = performance.now();
                await languageManager.getLanguages();
                const secondCallTime = performance.now() - startTime2;
                
                if (secondCallTime < firstCallTime) {
                    addTestResult('缓存机制', true, `缓存提升性能: ${firstCallTime.toFixed(2)}ms → ${secondCallTime.toFixed(2)}ms`);
                    results.push(`<div class="test-result success">✅ 缓存机制正常工作: ${firstCallTime.toFixed(2)}ms → ${secondCallTime.toFixed(2)}ms</div>`);
                } else {
                    addTestResult('缓存机制', false, '缓存机制可能未生效');
                    results.push('<div class="test-result info">ℹ️ 缓存机制可能未生效（或数据已在缓存中）</div>');
                }

                // 测试2.3: 数据完整性
                const languages = await languageManager.getLanguages();
                const requiredFields = ['id', 'name'];
                let allValid = true;
                
                languages.forEach(lang => {
                    requiredFields.forEach(field => {
                        if (!lang.hasOwnProperty(field)) {
                            allValid = false;
                        }
                    });
                });
                
                if (allValid) {
                    addTestResult('数据完整性', true, '所有语言数据包含必需字段');
                    results.push('<div class="test-result success">✅ 所有语言数据包含必需字段</div>');
                } else {
                    addTestResult('数据完整性', false, '部分语言数据缺少必需字段');
                    results.push('<div class="test-result error">❌ 部分语言数据缺少必需字段</div>');
                }

            } catch (error) {
                addTestResult('数据一致性验证', false, `数据一致性验证失败: ${error.message}`);
                results.push(`<div class="test-result error">❌ 数据一致性验证失败: ${error.message}</div>`);
            }
            
            updateResults('consistencyTests', results.join(''));
        }

        async function test3_ApiFormatConversion() {
            log('开始API格式转换测试');
            let results = [];
            
            try {
                const languageManager = getLanguageManager();
                
                // 测试3.1: 数组格式转换
                const testIds = [2, 3, 4];
                const arrayFormat = await languageManager.transformLanguageData(testIds, 'array');
                
                if (Array.isArray(arrayFormat) && arrayFormat.length === testIds.length) {
                    addTestResult('数组格式转换', true, '数组格式转换成功');
                    results.push('<div class="test-result success">✅ 数组格式转换成功</div>');
                    results.push(`<pre>输入: ${JSON.stringify(testIds)}\n输出: ${JSON.stringify(arrayFormat)}</pre>`);
                } else {
                    addTestResult('数组格式转换', false, '数组格式转换失败');
                    results.push('<div class="test-result error">❌ 数组格式转换失败</div>');
                }

                // 测试3.2: API格式转换
                const apiFormat = await languageManager.transformLanguageData(testIds, 'api');
                
                if (apiFormat && apiFormat.languages_id_array) {
                    addTestResult('API格式转换', true, 'API格式转换成功');
                    results.push('<div class="test-result success">✅ API格式转换成功</div>');
                    results.push(`<pre>API格式: ${JSON.stringify(apiFormat, null, 2)}</pre>`);
                } else {
                    addTestResult('API格式转换', false, 'API格式转换失败');
                    results.push('<div class="test-result error">❌ API格式转换失败</div>');
                }

                // 测试3.3: 专用API转换方法
                const apiObjectFormat = await languageManager.transformForAPI(testIds);
                
                if (typeof apiObjectFormat === 'object' && Object.keys(apiObjectFormat).length > 0) {
                    addTestResult('专用API转换', true, '专用API转换方法成功');
                    results.push('<div class="test-result success">✅ 专用API转换方法成功</div>');
                    results.push(`<pre>对象格式: ${JSON.stringify(apiObjectFormat, null, 2)}</pre>`);
                } else {
                    addTestResult('专用API转换', false, '专用API转换方法失败');
                    results.push('<div class="test-result error">❌ 专用API转换方法失败</div>');
                }

            } catch (error) {
                addTestResult('API格式转换测试', false, `API格式转换测试失败: ${error.message}`);
                results.push(`<div class="test-result error">❌ API格式转换测试失败: ${error.message}</div>`);
            }
            
            updateResults('apiFormatTests', results.join(''));
        }

        async function test4_DefaultValueStrategy() {
            log('开始默认值策略测试');
            let results = [];
            
            try {
                const languageManager = getLanguageManager();
                
                // 测试4.1: 表单上下文默认值
                const formDefaults = await languageManager.getDefaultSelection('form');
                
                if (Array.isArray(formDefaults) && formDefaults.length > 0) {
                    addTestResult('表单默认值', true, `表单默认值: ${formDefaults.join(', ')}`);
                    results.push(`<div class="test-result success">✅ 表单默认值正常: [${formDefaults.join(', ')}]</div>`);
                } else {
                    addTestResult('表单默认值', false, '表单默认值为空');
                    results.push('<div class="test-result error">❌ 表单默认值为空</div>');
                }

                // 测试4.2: AI分析上下文默认值
                const aiDefaults = await languageManager.getDefaultSelection('ai-analysis');
                
                if (Array.isArray(aiDefaults) && aiDefaults.length > 0) {
                    addTestResult('AI分析默认值', true, `AI分析默认值: ${aiDefaults.join(', ')}`);
                    results.push(`<div class="test-result success">✅ AI分析默认值正常: [${aiDefaults.join(', ')}]</div>`);
                } else {
                    addTestResult('AI分析默认值', false, 'AI分析默认值为空');
                    results.push('<div class="test-result error">❌ AI分析默认值为空</div>');
                }

                // 测试4.3: 多订单上下文默认值
                const multiOrderDefaults = await languageManager.getDefaultSelection('multi-order');
                
                if (Array.isArray(multiOrderDefaults) && multiOrderDefaults.length > 0) {
                    addTestResult('多订单默认值', true, `多订单默认值: ${multiOrderDefaults.length}个语言`);
                    results.push(`<div class="test-result success">✅ 多订单默认值正常: ${multiOrderDefaults.length}个语言</div>`);
                } else {
                    addTestResult('多订单默认值', false, '多订单默认值为空');
                    results.push('<div class="test-result error">❌ 多订单默认值为空</div>');
                }

            } catch (error) {
                addTestResult('默认值策略测试', false, `默认值策略测试失败: ${error.message}`);
                results.push(`<div class="test-result error">❌ 默认值策略测试失败: ${error.message}</div>`);
            }
            
            updateResults('defaultValueTests', results.join(''));
        }

        async function test5_PerformanceAndCaching() {
            log('开始性能和缓存测试');
            let results = [];
            
            try {
                const languageManager = getLanguageManager();
                
                // 测试5.1: 性能测试（多次调用）
                const iterations = 10;
                const times = [];
                
                for (let i = 0; i < iterations; i++) {
                    const start = performance.now();
                    await languageManager.getLanguages();
                    const end = performance.now();
                    times.push(end - start);
                }
                
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                const maxTime = Math.max(...times);
                const minTime = Math.min(...times);
                
                if (avgTime < 50) { // 平均响应时间小于50ms
                    addTestResult('性能测试', true, `平均响应时间: ${avgTime.toFixed(2)}ms`);
                    results.push(`<div class="test-result success">✅ 性能良好: 平均${avgTime.toFixed(2)}ms (最小: ${minTime.toFixed(2)}ms, 最大: ${maxTime.toFixed(2)}ms)</div>`);
                } else {
                    addTestResult('性能测试', false, `性能较慢: ${avgTime.toFixed(2)}ms`);
                    results.push(`<div class="test-result error">❌ 性能需要优化: 平均${avgTime.toFixed(2)}ms</div>`);
                }

                // 测试5.2: 缓存状态
                const status = languageManager.getStatus();
                
                if (status) {
                    addTestResult('缓存状态', true, '成功获取缓存状态');
                    results.push('<div class="test-result success">✅ 缓存状态正常</div>');
                    results.push(`<pre>缓存状态: ${JSON.stringify(status, null, 2)}</pre>`);
                } else {
                    addTestResult('缓存状态', false, '无法获取缓存状态');
                    results.push('<div class="test-result error">❌ 无法获取缓存状态</div>');
                }

                // 测试5.3: 内存使用情况
                if (performance.memory) {
                    const memoryUsage = {
                        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                    };
                    
                    if (memoryUsage.used < 50) { // 小于50MB
                        addTestResult('内存使用', true, `内存使用: ${memoryUsage.used}MB`);
                        results.push(`<div class="test-result success">✅ 内存使用合理: ${memoryUsage.used}MB / ${memoryUsage.total}MB</div>`);
                    } else {
                        addTestResult('内存使用', false, `内存使用过高: ${memoryUsage.used}MB`);
                        results.push(`<div class="test-result error">❌ 内存使用过高: ${memoryUsage.used}MB</div>`);
                    }
                } else {
                    results.push('<div class="test-result info">ℹ️ 浏览器不支持内存监控</div>');
                }

            } catch (error) {
                addTestResult('性能和缓存测试', false, `性能和缓存测试失败: ${error.message}`);
                results.push(`<div class="test-result error">❌ 性能和缓存测试失败: ${error.message}</div>`);
            }
            
            updateResults('performanceTests', results.join(''));
        }

        function updateOverview() {
            const passRate = testResults.total > 0 ? (testResults.passed / testResults.total * 100).toFixed(1) : 0;
            const overviewHtml = `
                <div class="test-result ${testResults.failed === 0 ? 'success' : 'error'}">
                    <strong>测试概览:</strong> 
                    总计 ${testResults.total} 项测试，
                    通过 ${testResults.passed} 项，
                    失败 ${testResults.failed} 项
                    (通过率: ${passRate}%)
                </div>
            `;
            updateResults('testOverview', overviewHtml);
        }

        function updateDetailedLog() {
            const logHtml = `<pre>${detailedLogs.join('\n')}</pre>`;
            updateResults('detailedLog', logHtml);
        }

        function clearResults() {
            const containers = ['testOverview', 'basicTests', 'consistencyTests', 'apiFormatTests', 'defaultValueTests', 'performanceTests', 'detailedLog'];
            containers.forEach(id => {
                updateResults(id, '等待测试...');
            });
            testResults = { total: 0, passed: 0, failed: 0, details: [] };
            detailedLogs = [];
        }

        async function showCacheStatus() {
            try {
                const languageManager = getLanguageManager();
                const status = languageManager.getStatus();
                alert(`缓存状态:\n${JSON.stringify(status, null, 2)}`);
            } catch (error) {
                alert(`获取缓存状态失败: ${error.message}`);
            }
        }

        // 页面加载完成后显示准备状态
        document.addEventListener('DOMContentLoaded', () => {
            log('语言数据统一管理系统验证测试页面已加载');
            updateResults('testOverview', '<div class="test-result info">ℹ️ 点击"运行所有测试"开始验证</div>');
        });
    </script>
</body>
</html>