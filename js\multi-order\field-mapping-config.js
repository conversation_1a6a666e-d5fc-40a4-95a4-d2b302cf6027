/**
 * @OTA_CONFIG 多订单字段映射配置中心
 * 🏷️ 标签: @OTA_FIELD_MAPPING_CONFIG
 * 📝 说明: 统一管理所有字段映射规则，确保数据转换的一致性和完整性
 * ⚠️ 警告: 修改此配置前请确保了解GoMyHire API规范
 * <AUTHOR>
 * @version 1.0.0
 */

// 防止重复加载
if (window.OTA && window.OTA.FieldMappingConfig) {
    console.log('字段映射配置已存在，跳过重复加载');
} else {

/**
 * 字段映射配置对象
 * 包含所有数据转换规则和验证逻辑
 */
const FIELD_MAPPING_CONFIG = {
    // Gemini AI返回字段 (snake_case) → 前端使用字段 (camelCase)
    AI_TO_FRONTEND: {
        'customer_name': 'customerName',
        'customer_contact': 'customerContact',
        'customer_email': 'customerEmail',
        'pickup_location': 'pickup',
        'dropoff_location': 'dropoff',
        'pickup_date': 'pickupDate',
        'pickup_time': 'pickupTime',
        'passenger_count': 'passengerCount',
        'luggage_count': 'luggageCount',
        'ota_price': 'otaPrice',
        'ota_reference_number': 'otaReferenceNumber',
        'extra_requirement': 'extraRequirement',
        'flight_info': 'flightInfo',
        'arrival_time': 'arrivalTime',
        'departure_time': 'departureTime',
        'sub_category_id': 'subCategoryId',
        'car_type_id': 'carTypeId',
        'driving_region_id': 'drivingRegionId',
        'languages_id_array': 'languagesIdArray',
        'meet_and_greet': 'meetAndGreet',
        'baby_chair': 'babyChair',
        'tour_guide': 'tourGuide'
    },

    // 前端字段 (camelCase) → GoMyHire API字段 (snake_case)
    FRONTEND_TO_API: {
        'pickup': 'pickup_location',
        'dropoff': 'dropoff_location', 
        'pickupDate': 'pickup_date',
        'pickupTime': 'pickup_time',
        'luggageCount': 'luggage_number', // 注意：API使用luggage_number而非luggage_count
        'customerContact': 'customer_contact',
        'customerName': 'customer_name',
        'customerEmail': 'customer_email',
        'otaPrice': 'ota_price',
        'otaReferenceNumber': 'ota_reference_number',
        'extraRequirement': 'extra_requirement',
        'passengerCount': 'passenger_count',
        'subCategoryId': 'sub_category_id',
        'carTypeId': 'car_type_id',
        'drivingRegionId': 'driving_region_id',
        'languagesIdArray': 'languages_id_array',
        'meetAndGreet': 'meet_and_greet',
        'babyChair': 'baby_chair',
        'tourGuide': 'tour_guide',
        'flightInfo': 'flight_info',
        'arrivalTime': 'arrival_time',
        'departureTime': 'departure_time'
    },

    // 备用字段名映射 - 处理不同数据源的字段名变体
    ALTERNATIVE_FIELDS: {
        'pickup': ['pickupLocation', 'pickup_location'],
        'dropoff': ['dropoffLocation', 'dropoff_location', 'destination'],
        'customerContact': ['phone', 'customer_contact', 'contact'],
        'customerName': ['customer_name', 'name'],
        'customerEmail': ['customer_email', 'email'],
        'price': ['otaPrice', 'ota_price', 'amount'],
        'otaPrice': ['price', 'ota_price', 'amount'],
        'pickupDate': ['pickup_date', 'date'],
        'pickupTime': ['pickup_time', 'time'],
        'passengerCount': ['passenger_count', 'passengers'],
        'luggageCount': ['luggage_count', 'luggage_number', 'luggage'],
        'extraRequirement': ['extra_requirement', 'special_requirement', 'notes'],
        'otaReferenceNumber': ['ota_reference_number', 'reference_number', 'booking_reference']
    },

    // GoMyHire API必填字段列表
    REQUIRED_API_FIELDS: [
        'pickup_location',
        'dropoff_location', 
        'pickup_date',
        'pickup_time',
        'customer_name',
        'ota_reference_number',
        'ota_price',
        'sub_category_id',
        'car_type_id',
        'driving_region_id',
        'languages_id_array'
    ],

    // 前端必填字段列表
    REQUIRED_FRONTEND_FIELDS: [
        'pickup',
        'dropoff',
        'pickupDate', 
        'pickupTime',
        'customerName',
        'otaReferenceNumber',
        'otaPrice',
        'subCategoryId',
        'carTypeId',
        'drivingRegionId',
        'languagesIdArray'
    ],

    // 字段数据类型定义
    FIELD_TYPES: {
        'customerName': 'string',
        'customerContact': 'string',
        'customerEmail': 'string',
        'pickup': 'string',
        'dropoff': 'string',
        'pickupDate': 'date',
        'pickupTime': 'time',
        'passengerCount': 'number',
        'luggageCount': 'number',
        'otaPrice': 'number',
        'otaReferenceNumber': 'string',
        'extraRequirement': 'string',
        'subCategoryId': 'number',
        'carTypeId': 'number',
        'drivingRegionId': 'number',
        'languagesIdArray': 'array',
        'meetAndGreet': 'boolean',
        'babyChair': 'boolean',
        'tourGuide': 'boolean'
    },

    // 特殊字段处理规则
    SPECIAL_FIELD_RULES: {
        // languages_id_array必须转换为对象格式以避免API错误
        'languagesIdArray': {
            toAPI: (value) => {
                if (Array.isArray(value)) {
                    const objectFormat = {};
                    value.forEach((id, index) => {
                        objectFormat[index.toString()] = id.toString();
                    });
                    return objectFormat;
                }
                return value;
            },
            fromAPI: (value) => {
                if (typeof value === 'object' && value !== null) {
                    return Object.values(value).map(id => parseInt(id));
                }
                return Array.isArray(value) ? value : [2]; // 默认英文
            }
        },

        // 价格字段处理
        'otaPrice': {
            toAPI: (value) => parseFloat(value) || 0,
            fromAPI: (value) => parseFloat(value) || 0
        },

        // 数量字段处理
        'passengerCount': {
            toAPI: (value) => parseInt(value) || 1,
            fromAPI: (value) => parseInt(value) || 1
        },

        'luggageCount': {
            toAPI: (value) => parseInt(value) || 0,
            fromAPI: (value) => parseInt(value) || 0
        }
    }
};

// 导出配置对象
window.OTA = window.OTA || {};
window.OTA.FieldMappingConfig = FIELD_MAPPING_CONFIG;

// 向后兼容
window.FIELD_MAPPING_CONFIG = FIELD_MAPPING_CONFIG;

console.log('✅ 字段映射配置已加载', {
    aiToFrontendMappings: Object.keys(FIELD_MAPPING_CONFIG.AI_TO_FRONTEND).length,
    frontendToApiMappings: Object.keys(FIELD_MAPPING_CONFIG.FRONTEND_TO_API).length,
    alternativeFields: Object.keys(FIELD_MAPPING_CONFIG.ALTERNATIVE_FIELDS).length
});

// 结束防重复加载检查
}
