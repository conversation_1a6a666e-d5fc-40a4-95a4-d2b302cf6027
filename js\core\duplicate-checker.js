/**
 * @OTA_CORE 重复开发检查器
 * 🏷️ 标签: @OTA_DUPLICATE_CHECKER
 * 📝 功能: 检测重复开发，防止38个文件重复定义getLogger等问题再次发生
 * ⚠️ 警告: 已注册，请勿重复开发
 * 
 * 解决发现的重复开发问题:
 * - getLogger函数在38个文件中重复定义
 * - getAppState函数在4个位置重复定义  
 * - getGeminiService函数在3个位置重复定义
 * - 100+全局变量污染window对象
 */

// 确保OTA命名空间存在
window.OTA = window.OTA || {};

(function() {
    'use strict';

    /**
     * @OTA_CORE 重复开发检查器
     * 实时监控全局变量和函数定义，预防重复开发
     */
    const DuplicateChecker = {
        // 已知的合法全局函数白名单
        whitelist: new Set([
            // 核心OTA函数
            'getService', 'getLogger', 'getAppState', 'getAPIService', 'getGeminiService',
            'getMultiOrderManager', 'getOrderHistoryManager', 'getImageUploadManager',
            'getCurrencyConverter', 'getI18nManager',
            
            // 浏览器原生函数
            'alert', 'confirm', 'prompt', 'console', 'setTimeout', 'setInterval',
            'fetch', 'XMLHttpRequest', 'addEventListener', 'removeEventListener',
            
            // OTA调试命令
            'otaRegistryReport', 'performSystemHealthCheck',
            
            // 历史兼容函数
            'getOtaConfigForUser'
        ]),
        
        // 检测到的重复项
        duplicates: new Map(),
        
        // 违规警告
        violations: [],
        
        /**
         * 扫描全局变量重复
         */
        checkForDuplicates() {
            const warnings = [];
            const globalFunctions = [];
            
            // 扫描所有以get开头的全局函数
            Object.keys(window).forEach(key => {
                if (key.startsWith('get') && typeof window[key] === 'function') {
                    globalFunctions.push(key);
                    
                    // 检查是否在白名单中
                    if (!this.whitelist.has(key)) {
                        warnings.push({
                            type: 'UNREGISTERED_GLOBAL_FUNCTION',
                            name: key,
                            message: `⚠️ 未注册的全局函数: ${key}`,
                            severity: 'warning',
                            suggestion: `请将 ${key} 注册到OTA.Registry或添加到白名单`
                        });
                    }
                }
            });
            
            // 检查重复定义
            this.checkDuplicateDefinitions(globalFunctions, warnings);
            
            // 检查命名规范
            this.checkNamingConventions(warnings);
            
            // 更新违规记录
            this.violations = warnings;
            
            return {
                summary: {
                    totalGlobalFunctions: globalFunctions.length,
                    whitelistedFunctions: globalFunctions.filter(f => this.whitelist.has(f)).length,
                    unregisteredFunctions: warnings.filter(w => w.type === 'UNREGISTERED_GLOBAL_FUNCTION').length,
                    totalWarnings: warnings.length
                },
                violations: warnings,
                globalFunctions,
                timestamp: new Date().toISOString()
            };
        },
        
        /**
         * 检查重复定义问题
         */
        checkDuplicateDefinitions(globalFunctions, warnings) {
            // 检查已知的重复问题模式
            const knownDuplicates = ['getLogger', 'getAppState', 'getGeminiService'];
            
            knownDuplicates.forEach(funcName => {
                if (globalFunctions.includes(funcName)) {
                    // 检查是否有多个定义源
                    const registry = window.OTA && window.OTA.Registry;
                    if (registry && registry.duplicateDetections.has(funcName)) {
                        const count = registry.duplicateDetections.get(funcName);
                        if (count > 0) {
                            warnings.push({
                                type: 'DUPLICATE_FUNCTION_DEFINITION',
                                name: funcName,
                                message: `🚨 检测到重复定义: ${funcName} (重复次数: ${count})`,
                                severity: 'error',
                                suggestion: `请移除 ${funcName} 的重复定义，只保留核心文件中的版本`
                            });
                        }
                    }
                }
            });
        },
        
        /**
         * 检查命名规范
         */
        checkNamingConventions(warnings) {
            Object.keys(window).forEach(key => {
                // 检查是否有未标记的OTA相关函数
                if ((key.includes('ota') || key.includes('OTA')) && 
                    typeof window[key] === 'function' && 
                    !this.whitelist.has(key)) {
                    warnings.push({
                        type: 'NAMING_CONVENTION_VIOLATION',
                        name: key,
                        message: `📋 OTA相关函数未标记: ${key}`,
                        severity: 'info',
                        suggestion: `请为 ${key} 添加@OTA_标签并注册到Registry`
                    });
                }
            });
        },
        
        /**
         * 实时监控模式
         */
        startRealTimeMonitoring() {
            // 每30秒检查一次
            setInterval(() => {
                const result = this.checkForDuplicates();
                if (result.violations.length > 0) {
                    console.group('🚨 重复开发检测警告');
                    result.violations.forEach(violation => {
                        const logLevel = violation.severity === 'error' ? 'error' : 
                                       violation.severity === 'warning' ? 'warn' : 'info';
                        console[logLevel](violation.message, violation);
                    });
                    console.groupEnd();
                }
            }, 30000);
            
            console.log('🔍 重复开发实时监控已启动 (30秒检查间隔)');
        },
        
        /**
         * 生成架构违规报告
         */
        generateViolationReport() {
            const result = this.checkForDuplicates();
            const report = {
                timestamp: new Date().toISOString(),
                summary: result.summary,
                violations: result.violations,
                recommendations: this.generateRecommendations(result.violations),
                healthScore: this.calculateHealthScore(result)
            };
            
            return report;
        },
        
        /**
         * 生成修复建议
         */
        generateRecommendations(violations) {
            const recommendations = [];
            
            // 按类型分组建议
            const byType = violations.reduce((acc, v) => {
                acc[v.type] = acc[v.type] || [];
                acc[v.type].push(v);
                return acc;
            }, {});
            
            Object.keys(byType).forEach(type => {
                const items = byType[type];
                switch(type) {
                    case 'DUPLICATE_FUNCTION_DEFINITION':
                        recommendations.push({
                            priority: 'HIGH',
                            action: 'REMOVE_DUPLICATES',
                            description: `移除 ${items.length} 个重复函数定义`,
                            items: items.map(i => i.name)
                        });
                        break;
                    case 'UNREGISTERED_GLOBAL_FUNCTION':
                        recommendations.push({
                            priority: 'MEDIUM',
                            action: 'REGISTER_FUNCTIONS',
                            description: `注册 ${items.length} 个未注册的全局函数`,
                            items: items.map(i => i.name)
                        });
                        break;
                    case 'NAMING_CONVENTION_VIOLATION':
                        recommendations.push({
                            priority: 'LOW',
                            action: 'ADD_TAGS',
                            description: `为 ${items.length} 个函数添加@OTA_标签`,
                            items: items.map(i => i.name)
                        });
                        break;
                }
            });
            
            return recommendations;
        },
        
        /**
         * 计算架构健康评分
         */
        calculateHealthScore(result) {
            const { violations } = result;
            const totalIssues = violations.length;
            const errorCount = violations.filter(v => v.severity === 'error').length;
            const warningCount = violations.filter(v => v.severity === 'warning').length;
            
            // 基础分100分，根据问题扣分
            let score = 100;
            score -= errorCount * 10;    // 错误扣10分
            score -= warningCount * 5;   // 警告扣5分
            score -= (totalIssues - errorCount - warningCount) * 2; // 信息扣2分
            
            return {
                score: Math.max(0, score),
                grade: score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : score >= 60 ? 'D' : 'F',
                issues: {
                    total: totalIssues,
                    errors: errorCount,
                    warnings: warningCount,
                    infos: totalIssues - errorCount - warningCount
                }
            };
        }
    };

    // 注册到OTA命名空间
    window.OTA.DuplicateChecker = DuplicateChecker;
    
    // 全局访问（向后兼容）
    window.DuplicateChecker = DuplicateChecker;
    
    // 注册到OTA注册中心
    if (window.OTA && window.OTA.Registry) {
        window.OTA.Registry.registerUtil('DuplicateChecker', DuplicateChecker, '@OTA_DUPLICATE_CHECKER');
    }
    
    // 导出全局调试命令
    window.checkDuplicates = () => {
        const report = DuplicateChecker.generateViolationReport();
        console.group('🔍 重复开发检查报告');
        console.log('📊 健康评分:', report.healthScore);
        console.table(report.summary);
        if (report.violations.length > 0) {
            console.group('⚠️ 发现的违规项');
            report.violations.forEach(v => console.log(v.message, v));
            console.groupEnd();
        }
        if (report.recommendations.length > 0) {
            console.group('💡 修复建议');
            report.recommendations.forEach(r => console.log(r));
            console.groupEnd();
        }
        console.groupEnd();
        return report;
    };
    
    window.startDuplicateMonitoring = () => {
        DuplicateChecker.startRealTimeMonitoring();
    };
    
    // 初始化日志
    const logger = window.getLogger ? window.getLogger() : console;
    if (logger && logger.log) {
        logger.log('🚀 重复开发检查器已初始化', 'info', {
            whitelistSize: DuplicateChecker.whitelist.size,
            features: ['实时监控', '重复检测', '架构评分']
        });
    }

})();