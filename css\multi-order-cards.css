/* 多订单卡片样式 - 直立卡片设计，粉红主题配色，移动端优化 */

/* 
 * 注意：CSS变量已在 base/variables.css 中统一定义
 * 这里使用现有变量：var(--color-primary) 等
 */

/* 多订单面板基础样式 */
.multi-order-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: var(--overlay-backdrop);
    z-index: var(--z-overlay); /* 使用遮罩层级别，确保在最顶层 */
    overflow-y: auto;
    padding: 16px;
    box-sizing: border-box;
    display: none; /* 默认隐藏 */
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
.multi-order-panel:not(.hidden) {
    display: flex !important;
}
.multi-order-content {
    max-width: 1200px;
    width: 90vw;
    margin: 20px auto;
    background: var(--bg-tertiary);
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    position: relative;
}
/* 多订单组件专用样式 - 使用全局变量支持暗色模式 */
.multi-order-panel,
.multi-order-content,
.order-card {
  /* 组件使用全局变量，无需重复定义 */
}
/* 多订单头部样式 - 使用新色彩系统 */
.multi-order-header {
    background: var(--brand-gradient);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 50px; /* 按设计文档要求 */
}
.multi-order-header h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}
/* 头部左侧区域 */
.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}
.btn-header-back {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
}
.btn-header-back:hover {
    background: var(--button-overlay-medium);
    transform: translateX(-2px);
}
/* 订单统计样式已移除 */

.btn-header-action {
    background: var(--button-overlay-strong);
    border: 1px solid var(--button-overlay-strong);
    color: var(--color-primary);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-sm);
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: all var(--transition-fast);
}
.btn-header-action:hover {
    background: var(--color-white);
    transform: translateY(-1px);
}
/* 筛选排序控制栏 - 按设计文档要求40px高度 */
.filter-sort-controls {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-sm) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 40px;
    backdrop-filter: var(--blur-glass);
    -webkit-backdrop-filter: var(--blur-glass);
}
.filter-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}
.control-label {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-accent);
    white-space: nowrap;
}
.filter-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: var(--font-sm);
    min-width: 80px;
    cursor: pointer;
}
.filter-select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px var(--brand-glass);
}
.view-toggle {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-sm);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
}
.view-toggle.active {
    background: var(--color-primary);
    color: var(--color-white);
}
.quick-actions {
    display: flex;
    gap: var(--spacing-sm);
}
.quick-btn {
    background: var(--brand-glass);
    border: 1px solid var(--color-primary);
    color: var(--text-accent);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}
.quick-btn:hover {
    background: var(--color-primary);
    color: var(--color-white);
    transform: translateY(-1px);
}
.multi-order-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}
.order-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;
}
.order-count {
    font-weight: 600;
    font-size: 1rem;
}
/* date-range 样式已移除 */

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}
.header-actions .btn {
    padding: 6px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
}
/* 响应式布局优化 - 保持水平排列但优化间距 */
@media (max-width: 768px) {




    .dropdown-arrow {
    font-size: 12px;
    transition: transform var(--transition-fast) ease;
}


.btn-create-all {
    background: var(--color-success-gradient);
    color: var(--color-white);
    grid-column: 1 / -1; /* 跨两列 */
}
.btn-select-all {
    background: var(--color-info-gradient);
    color: var(--color-white);
}
.btn-clear-all {
    background: var(--color-error-gradient);
    color: var(--color-white);
}
}

/* ========================================
   订单卡片网格容器优化
   ======================================== */

/* 订单卡片网格 - 移动端优化 */
.multi-order-list {
    padding: var(--spacing-2);
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: var(--spacing-3);
    background: var(--bg-primary);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    min-height: 300px;
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    border-radius: 0;
}

/* 移动端网格布局调整 */
@media (max-width: 768px) {
    .multi-order-list {
        grid-template-columns: 1fr; /* 单列布局 */
        gap: var(--spacing-2);
        padding: var(--spacing-2);
        max-height: calc(100vh - 160px);
    }
}

@media (max-width: 480px) {
    .multi-order-list {
        padding: var(--spacing-1);
        gap: var(--spacing-1);
        max-height: calc(100vh - 140px);
    }
}

@media (max-width: 375px) {
    .multi-order-list {
        padding: 2px;
        gap: 2px;
        max-height: calc(100vh - 120px);
    }
}
/* 多订单底部样式 - 重新设计为两层布局 */
.multi-order-footer {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
    border-top: 1px solid var(--button-overlay-medium);
    border-radius: 0 0 16px 16px;
    position: sticky;
    bottom: 0;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
}
.footer-actions-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-2);
    flex-wrap: wrap;
}
/* 底部操作按钮分组 */
.footer-actions-left,
.footer-actions-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}
.footer-actions-center {
    flex: 1;
    text-align: center;
}
/* Footer按钮样式 - 与导航栏颜色同步 */
.btn-footer {
    background: var(--button-overlay-light);
    border: 1px solid var(--button-overlay-medium);
    color: var(--color-white);
    padding: 2px 6px; /* 减少30%：原本更大的padding */
    font-size: 0.85rem; /* 减少15%：1rem → 0.85rem */
    border-radius: 4px;
    transition: all var(--transition-fast);
    min-height: auto;
}
.btn-footer:hover {
    background: var(--button-overlay-medium);
    transform: translateY(-1px);
}
.btn-footer-primary {
    background: var(--button-overlay-strong);
    border: 1px solid var(--button-overlay-strong);
    color: var(--color-primary);
    padding: 2px 8px; /* 略大一点作为主要按钮 */
    font-size: 0.85rem; /* 减少15% */
    font-weight: 600;
    border-radius: 4px;
    transition: all var(--transition-fast);
    min-height: auto;
}
.btn-footer-primary:hover {
    background: var(--color-white);
    transform: translateY(-1px) scale(1.02);
}
.footer-count {
    color: var(--color-white);
    font-size: 0.85rem; /* 减少15% */
    font-weight: 500;
    white-space: nowrap;
    padding: 0 4px;
}
/* Footer响应式设计 */
@media (max-width: 768px) {
    
    .footer-actions-row {
        justify-content: center;
        gap: 2px;
    }
    
    .btn-footer,
    .btn-footer-primary {
        padding: 1px 4px;
        font-size: 0.8rem; /* 进一步减小 */
    }
    
    .footer-count {
        font-size: 0.8rem;
        order: -1; /* 将计数移到前面 */
        flex: 1;
        text-align: center;
    }
}

@media (max-width: 480px) {
    
    .multi-order-footer {
        padding: 2px 4px;
        min-height: 20px;
    }
    
    .footer-actions-row {
        gap: 1px;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    
    .btn-footer,
    .btn-footer-primary {
        padding: 1px 3px;
        font-size: 0.75rem;
        min-width: auto;
    }
    
    .footer-count {
        font-size: 0.75rem;
        width: 100%;
        text-align: center;
        margin-bottom: 2px;
    }
}


/* 紧凑卡片设计 - 按设计文档要求 */
.order-card {
    background: var(--bg-tertiary);
    border-radius: 12px;
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal) ease;
    overflow: hidden;
    border: 1px solid var(--border-color);
    position: relative;
    min-height: 80px; /* 按设计文档紧凑要求 */
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
}
.order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px -5px var(--brand-overlay-strong);
    border-color: var(--color-primary);
}
.order-card.selected {
    border-color: var(--color-primary);
    background: linear-gradient(135deg, var(--brand-glass) 0%, var(--brand-overlay-subtle) 100%);
}
/* 紧凑同行布局样式 */
.compact-inline-layout {
    --item-height: 24px;
    --item-spacing: 4px;
    --font-size: var(--font-sm);
    --line-height: var(--line-height-tight);
}
.inline-item {
    display: inline-flex;
    align-items: center;
    height: var(--item-height);
    margin-right: var(--item-spacing);
    font-size: var(--font-size);
    line-height: var(--line-height);
    white-space: nowrap;
}
.inline-label {
    font-weight: 600;
    margin-right: var(--spacing-xs);
    color: var(--color-primary);
}
.inline-value {
    color: var(--text-secondary);
}
/* 状态图标样式 */
.status-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: var(--spacing-xs);
    vertical-align: middle;
}
.status-ready { color: var(--color-success-green); }
.status-warning { color: var(--color-warning-amber); }
.status-error { color: var(--color-error-red); }
.status-processing { color: var(--color-info-blue); }
.status-completed { color: var(--color-success-green); }
.status-cancelled { color: var(--color-error-red); }
.status-complete { color: var(--color-success-green); }
.status-progress { color: var(--color-warning-amber); }
.status-cancel { color: var(--color-error-red); }
.status-normal { color: var(--color-success-green); }
.order-card.paging-order {
    border-left: 4px solid var(--color-warning-yellow);
}
/* 卡片头部 */
.order-card-header {
    padding: 1px 4px;
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    color: var(--color-white);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 15px;
}
.order-selector {
    display: flex;
    align-items: center;
    gap: 10px;
}
.order-checkbox {
    width: 18px;
    height: 18px;
    accent-color: var(--color-white);
}
.order-title {
    display: flex;
    align-items: center;
    gap: 8px;
}
.order-number {
    font-weight: 600;
    font-size: 14px;
}
.paging-badge {
    background: var(--warning-bg-overlay);
    color: var(--color-warning-yellow);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
}
.order-status {
    display: flex;
    align-items: center;
}
.status-badge {
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
}
.status-ready {
    background: var(--success-bg-overlay);
    color: var(--color-success-green);
}
/* 卡片主体 */
.order-card-body {
    padding: 1px 4px;
    cursor: pointer;
}
.order-card-body:hover {
    background: var(--color-primary-bg);
}
/* 卡片底部操作区 */
.order-card-footer {
    padding: 1px 4px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    min-height: 32px;
}
/* ========================================
   订单操作区域样式优化
   ======================================== */

/* 订单操作容器 */
.order-actions {
    padding: var(--spacing-2);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 12px 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-2);
}

/* 订单卡片操作按钮 - 移动端优化 */
.btn-card-action {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-1);
    padding: var(--spacing-2) var(--spacing-3);
    border: none;
    border-radius: 8px;
    font-size: var(--font-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    min-height: 36px; /* 确保触摸目标 */
    min-width: 80px;
    -webkit-backdrop-filter: var(--blur-glass);
    backdrop-filter: var(--blur-glass);
}

.btn-create {
    background: var(--color-success-gradient);
    color: var(--color-white);
    width: 100%;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-success-green);
}

.btn-create:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--success-shadow);
    background: linear-gradient(135deg, var(--color-success-green) 0%, var(--color-success-dark) 100%);
}

.btn-create:active {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 16px;
    opacity: 0.9;
}

.btn-text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* 移动端按钮优化 */
@media (max-width: 768px) {
    .order-actions {
        padding: var(--spacing-2);
    }

    .btn-card-action {
        min-height: 40px; /* 增大触摸目标 */
        font-size: 15px;
        padding: var(--spacing-2) var(--spacing-4);
    }

    .btn-icon {
        font-size: 18px;
    }
}

@media (max-width: 480px) {
    .btn-card-action {
        min-height: 44px; /* 44px最小触摸目标 */
        font-size: 16px;
        font-weight: 700;
        border-radius: 10px;
    }

    .btn-icon {
        font-size: 20px;
    }
}

@media (max-width: 375px) {
    .order-actions {
        padding: var(--spacing-1);
    }

    .btn-card-action {
        min-height: 48px; /* 更大的触摸目标 */
        font-size: 17px;
        padding: var(--spacing-3);
        border-radius: 12px;
    }
}
/* 移动端优化 - 方案一：紧凑高效型 */

/* 平板端优化 (768px-481px) */
@media (max-width: 768px) and (min-width: 481px) {
    
    .multi-order-panel {
        padding: 12px;
        justify-content: flex-start;
        padding-top: 40px;
    }

    /* 移动端底部操作栏优化 */
    .footer-actions-row {
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .footer-actions-left,
    .footer-actions-right {
        width: 100%;
        justify-content: center;
    }

    .footer-actions-center {
        order: -1; /* 将计数器移到顶部 */
    }
    
    .multi-order-content {
        width: 95vw;
        margin: 0 auto;
    }
}


/* 大屏手机端优化 (480px-376px) - 智能双列布局 */
@media (max-width: 480px) and (min-width: 376px) {
    
    .multi-order-panel {
        padding: 8px;
        justify-content: flex-start;
        padding-top: 30px;
    }
    
    .multi-order-content {
        width: 96vw;
        margin: 0 auto;
    }
}


/* 小屏手机端优化 (375px以下) - 单列紧凑布局 */
@media (max-width: 375px) {
    
    .multi-order-panel {
        padding: 6px;
        justify-content: flex-start;
        padding-top: 20px;
    }
    
    .multi-order-content {
        width: 98vw;
        margin: 0 auto;
    }
}


/* 极小屏幕优化 (320px以下) */
@media (max-width: 320px) {
    
    .multi-order-panel {
        padding: 4px;
    }
}


/* 移动端专用增强样式 */
@media (max-width: 768px) {


    /* 移动端增强样式可在此添加 */
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
    
    .multi-order-panel {
        padding: 8px 16px;
    }
}


/* 高对比度模式支持 */
@media (prefers-contrast: high) {


    /* 高对比度样式可在此添加 */
}

/* ========================================
   订单网格布局样式 - 核心缺失样式修复
   ======================================== */

/* 订单网格主容器 */
.order-grid-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs);
    background: var(--bg-tertiary);
    border-radius: 8px;
    margin-bottom: var(--spacing-xs);
}

/* 左右分栏容器 */
.order-grid-left,
.order-grid-right {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
    min-width: 0; /* 防止内容溢出 */
}

/* 网格项目基础样式 */
.grid-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-2) var(--spacing-xs);
    background: var(--brand-overlay-minimal);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
    min-height: 32px; /* 确保触摸目标 */
    position: relative;
    overflow: hidden;
}

.grid-item:hover {
    background: var(--brand-overlay-subtle);
    border-color: var(--color-primary);
    transform: translateY(-1px);
}

/* 可编辑字段样式 */
.editable-field {
    cursor: pointer;
    background: linear-gradient(135deg, var(--brand-glass) 0%, var(--brand-overlay-minimal) 100%);
}

.editable-field:hover {
    background: linear-gradient(135deg, var(--brand-overlay-subtle) 0%, var(--brand-overlay-medium) 100%);
    box-shadow: 0 2px 8px var(--brand-overlay-extra);
}

.editable-field:active {
    transform: translateY(0);
    box-shadow: inset 0 2px 4px var(--brand-overlay-medium);
}

/* 网格标签样式 */
.grid-label {
    font-size: 14px;
    min-width: 20px;
    text-align: center;
    flex-shrink: 0;
    opacity: 0.8;
}

/* 网格值样式 */
.grid-value {
    flex: 1;
    font-size: var(--font-sm);
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.3;
    word-break: break-word;
    overflow: hidden;
}

/* 编辑指示器 */
.edit-indicator {
    font-size: 12px;
    opacity: 0;
    transition: opacity var(--transition-fast);
    margin-left: auto;
    flex-shrink: 0;
}

.editable-field:hover .edit-indicator {
    opacity: 0.7;
}

/* 路线显示特殊样式 */
.grid-item-route {
    grid-column: 1 / -1; /* 跨两列显示 */
    background: var(--brand-overlay-subtle);
    border: 1px solid var(--color-primary-light);
}

.route-display {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.pickup-address,
.dropoff-address {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-xs);
}

.address-label {
    font-weight: 600;
    color: var(--color-primary);
    min-width: 32px;
    flex-shrink: 0;
}

.address-text {
    color: var(--text-secondary);
    line-height: 1.2;
    word-break: break-word;
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.order-card {
    animation: slideIn 0.3s ease-out;
}
/* 关闭按钮样式 */
.multi-order-close-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: var(--overlay-backdrop);
    color: var(--color-white);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    z-index: 1002;
    transition: all var(--transition-fast) ease;
}
.multi-order-close-btn:hover {
    background: var(--shadow-darker);
}
/* 移动端关闭按钮优化 */
@media (max-width: 768px) {
    
    .multi-order-close-btn {
        top: 12px;
        right: 12px;
        width: 44px;
        height: 44px;
        font-size: 20px;
        background: rgba(0, 0, 0, 0.6);
        -webkit-backdrop-filter: var(--blur-glass);
        backdrop-filter: var(--blur-glass);
        border: 2px solid var(--button-overlay-medium);
    }

    .multi-order-close-btn:active {
        transform: scale(0.95);
        background: var(--shadow-darkest);
    }
}

@media (max-width: 375px) {
    
    .multi-order-close-btn {
        top: 8px;
        right: 8px;
        width: 48px;
        height: 48px;
        font-size: 22px;
    }
}

/* 紧凑按钮样式 */
.btn-compact {
    padding: 2px 6px;
    font-size: var(--font-sm);
    height: 20px;
    min-width: 40px;
    border-radius: 4px;
    border: 1px solid var(--color-primary);
    background: var(--brand-gradient);
    color: var(--color-white);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    margin-right: 2px;
}
.btn-compact:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px var(--brand-overlay-extra);
}
/* 三列移动端布局 */
.three-column-mobile {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 4px;
    height: calc(100vh - 70px);
    overflow: hidden;
    padding: 4px;
}
.column-mobile {
    overflow-y: auto;
    padding: 4px;
    border-radius: 8px;
    background: var(--brand-overlay-minimal);
}
/* 滚动条样式已在 base/reset.css 中统一定义 */

/* ========================================
   移动端响应式优化 - 订单网格布局
   ======================================== */

/* 平板端优化 (768px-993px) */
@media (max-width: 992px) {
    .order-grid-layout {
        gap: var(--spacing-1);
        padding: var(--spacing-2);
    }

    .grid-item {
        min-height: 30px;
        padding: var(--spacing-1) var(--spacing-xs);
    }

    .grid-value {
        font-size: var(--font-xs);
    }
}

/* 大屏手机端优化 (481px-768px) */
@media (max-width: 768px) {
    .order-grid-layout {
        grid-template-columns: 1fr; /* 改为单列布局 */
        gap: var(--spacing-1);
        padding: var(--spacing-2);
    }

    .order-grid-left,
    .order-grid-right {
        gap: var(--spacing-1);
    }

    .grid-item {
        min-height: 36px; /* 增加触摸目标 */
        padding: var(--spacing-2);
        font-size: var(--font-sm);
    }

    .grid-label {
        font-size: 16px; /* 增大图标 */
        min-width: 24px;
    }

    .grid-value {
        font-size: var(--font-sm);
        font-weight: 500;
    }

    .route-display {
        gap: var(--spacing-2);
    }

    .pickup-address,
    .dropoff-address {
        font-size: var(--font-sm);
        gap: var(--spacing-2);
    }

    .address-label {
        min-width: 36px;
        font-size: var(--font-sm);
    }
}

/* 中屏手机端优化 (376px-480px) */
@media (max-width: 480px) {
    .order-grid-layout {
        padding: var(--spacing-1);
        gap: 2px;
    }

    .grid-item {
        min-height: 40px; /* 确保44px触摸目标 */
        padding: var(--spacing-2) var(--spacing-1);
        border-radius: 4px;
    }

    .grid-label {
        font-size: 18px;
        min-width: 28px;
    }

    .grid-value {
        font-size: 14px;
        line-height: 1.4;
    }

    .edit-indicator {
        font-size: 14px;
    }
}

/* 小屏手机端优化 (320px-375px) */
@media (max-width: 375px) {
    .order-grid-layout {
        padding: 2px;
        gap: 1px;
        border-radius: 6px;
    }

    .grid-item {
        min-height: 44px; /* 严格遵守44px最小触摸目标 */
        padding: var(--spacing-2);
        border-radius: 6px;
    }

    .grid-label {
        font-size: 20px;
        min-width: 32px;
    }

    .grid-value {
        font-size: 15px;
        font-weight: 600;
        line-height: 1.3;
    }

    .route-display {
        gap: var(--spacing-1);
    }

    .pickup-address,
    .dropoff-address {
        font-size: 13px;
        min-height: 20px;
        align-items: flex-start;
        padding: 2px 0;
    }

    .address-label {
        min-width: 40px;
        font-size: 13px;
        font-weight: 700;
    }

    .address-text {
        font-size: 13px;
        line-height: 1.2;
    }
}

/* 极小屏幕优化 (320px以下) */
@media (max-width: 320px) {
    .order-grid-layout {
        padding: 1px;
        gap: 1px;
    }

    .grid-item {
        min-height: 48px; /* 更大的触摸目标 */
        padding: var(--spacing-2) var(--spacing-1);
    }

    .grid-label {
        font-size: 22px;
        min-width: 36px;
    }

    .grid-value {
        font-size: 16px;
        font-weight: 700;
    }
}
